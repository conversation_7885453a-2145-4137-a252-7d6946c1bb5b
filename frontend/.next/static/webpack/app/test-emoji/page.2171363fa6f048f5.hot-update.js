"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/test-emoji/page",{

/***/ "(app-pages-browser)/./src/components/comment/WalineComment.tsx":
/*!**************************************************!*\
  !*** ./src/components/comment/WalineComment.tsx ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WalineComment: function() { return /* binding */ WalineComment; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _waline_client__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @waline/client */ \"(app-pages-browser)/./node_modules/@waline/client/dist/slim.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-themes */ \"(app-pages-browser)/./node_modules/next-themes/dist/index.module.js\");\n/* harmony import */ var _lib_walineConfig__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/walineConfig */ \"(app-pages-browser)/./src/lib/walineConfig.ts\");\n/* __next_internal_client_entry_do_not_use__ WalineComment,default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction WalineComment({ path, title, className = \"\" }) {\n    _s();\n    const walineRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const { resolvedTheme } = (0,next_themes__WEBPACK_IMPORTED_MODULE_3__.useTheme)();\n    const [walineInstance, setWalineInstance] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [isInitialized, setIsInitialized] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const initTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const abortControllerRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    // 安全销毁实例的函数\n    const safeDestroy = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((instance)=>{\n        if (instance && typeof instance.destroy === \"function\") {\n            try {\n                instance.destroy();\n            } catch (error) {\n                console.warn(\"Waline destroy error:\", error);\n            }\n        }\n    }, []);\n    // 生成随机头像URL\n    const generateRandomAvatar = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((seed)=>{\n        const avatarStyles = [\n            \"adventurer\",\n            \"adventurer-neutral\",\n            \"avataaars\",\n            \"avataaars-neutral\",\n            \"bottts\",\n            \"bottts-neutral\",\n            \"fun-emoji\",\n            \"icons\",\n            \"identicon\",\n            \"initials\",\n            \"lorelei\",\n            \"micah\",\n            \"miniavs\",\n            \"open-peeps\",\n            \"personas\",\n            \"pixel-art\",\n            \"pixel-art-neutral\"\n        ];\n        const styleIndex = seed.split(\"\").reduce((acc, char)=>acc + char.charCodeAt(0), 0) % avatarStyles.length;\n        const selectedStyle = avatarStyles[styleIndex];\n        return `https://api.dicebear.com/9.x/${selectedStyle}/svg?seed=${encodeURIComponent(seed)}&backgroundColor=transparent&radius=50`;\n    }, []);\n    // 增强头像显示\n    const enhanceAvatars = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(()=>{\n        if (!walineRef.current) return;\n        const container = walineRef.current;\n        const avatars = container.querySelectorAll(\".wl-avatar\");\n        avatars.forEach((avatar)=>{\n            if (!avatar.classList.contains(\"wl-avatar-enhanced\")) {\n                avatar.classList.add(\"wl-avatar-enhanced\");\n                const img = avatar.querySelector(\"img\");\n                if (!img) {\n                    const commentCard = avatar.closest(\".wl-card\");\n                    const userNick = commentCard?.querySelector(\".wl-nick\")?.textContent || \"anonymous\";\n                    const userMail = commentCard?.querySelector(\".wl-mail\")?.textContent || \"\";\n                    const seed = userMail || userNick || `user-${Math.random().toString(36).substr(2, 9)}`;\n                    const avatarUrl = generateRandomAvatar(seed);\n                    const avatarImg = document.createElement(\"img\");\n                    avatarImg.src = avatarUrl;\n                    avatarImg.alt = `${userNick}'s avatar`;\n                    avatarImg.style.cssText = `\n            width: calc(100% - 4px) !important;\n            height: calc(100% - 4px) !important;\n            border-radius: 50% !important;\n            object-fit: cover !important;\n            position: absolute !important;\n            top: 2px !important;\n            left: 2px !important;\n            z-index: 1 !important;\n            transition: all 0.3s ease !important;\n          `;\n                    avatarImg.onerror = ()=>{\n                        avatar.setAttribute(\"data-no-avatar\", \"true\");\n                        avatarImg.remove();\n                    };\n                    avatar.appendChild(avatarImg);\n                    avatar.removeAttribute(\"data-no-avatar\");\n                }\n            }\n        });\n    }, [\n        generateRandomAvatar\n    ]);\n    // 修复表情弹窗定位和层级问题\n    const fixEmojiPopups = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(()=>{\n        if (!walineRef.current) return;\n        const container = walineRef.current;\n        // 修复表情包图片加载 - 增强版\n        const fixEmojiImages = ()=>{\n            const emojiImages = container.querySelectorAll(\".wl-emoji img\");\n            console.log(`找到 ${emojiImages.length} 个表情包图片`);\n            emojiImages.forEach((img, index)=>{\n                const imgElement = img;\n                // 添加加载状态\n                imgElement.classList.add(\"loading\");\n                // 检查图片是否已经加载完成\n                if (imgElement.complete && imgElement.naturalWidth > 0) {\n                    console.log(`表情包 ${index} 已加载完成:`, imgElement.src);\n                    imgElement.classList.remove(\"loading\");\n                    return;\n                }\n                // 添加加载成功处理\n                const handleLoad = ()=>{\n                    console.log(`表情包 ${index} 加载成功:`, imgElement.src);\n                    imgElement.classList.remove(\"loading\", \"error\");\n                    imgElement.style.backgroundColor = \"transparent\";\n                    imgElement.style.border = \"none\";\n                };\n                // 添加加载失败处理\n                const handleError = ()=>{\n                    console.warn(`表情包 ${index} 加载失败:`, imgElement.src);\n                    imgElement.classList.remove(\"loading\");\n                    imgElement.classList.add(\"error\");\n                    // 尝试使用备用CDN\n                    const originalSrc = imgElement.src;\n                    if (originalSrc.includes(\"cdn.jsdelivr.net\")) {\n                        const backupSrc = originalSrc.replace(\"cdn.jsdelivr.net/npm\", \"unpkg.com\");\n                        console.log(`尝试备用CDN:`, backupSrc);\n                        imgElement.src = backupSrc;\n                    } else if (originalSrc.includes(\"unpkg.com\")) {\n                        const backupSrc = originalSrc.replace(\"unpkg.com\", \"cdn.jsdelivr.net/npm\");\n                        console.log(`尝试备用CDN:`, backupSrc);\n                        imgElement.src = backupSrc;\n                    }\n                };\n                // 移除旧的事件监听器（如果存在）\n                imgElement.removeEventListener(\"load\", handleLoad);\n                imgElement.removeEventListener(\"error\", handleError);\n                // 添加新的事件监听器\n                imgElement.addEventListener(\"load\", handleLoad, {\n                    once: true\n                });\n                imgElement.addEventListener(\"error\", handleError, {\n                    once: true\n                });\n                // 如果图片src为空或无效，尝试重新设置\n                if (!imgElement.src || imgElement.src === window.location.href) {\n                    console.warn(`表情包 ${index} src无效，尝试修复`);\n                // 这里可以添加重新获取src的逻辑\n                }\n            });\n        };\n        // 修复表情弹窗 - 使用新的选择器\n        const emojiButtons = container.querySelectorAll(\"[data-waline] .wl-action\");\n        emojiButtons.forEach((button)=>{\n            if (button.textContent?.includes(\"\\uD83D\\uDE00\") || button.getAttribute(\"title\")?.includes(\"emoji\")) {\n                button.addEventListener(\"click\", (e)=>{\n                    setTimeout(()=>{\n                        // 查找表情包弹窗 - 支持多种可能的类名\n                        const emojiPopup = container.querySelector(\"[data-waline] .wl-emoji\") || container.querySelector(\"[data-waline] .wl-emoji-popup\");\n                        if (emojiPopup) {\n                            // 确保弹窗正确显示\n                            emojiPopup.classList.add(\"wl-emoji-open\");\n                            emojiPopup.style.display = \"flex\";\n                            emojiPopup.style.opacity = \"1\";\n                            emojiPopup.style.visibility = \"visible\";\n                            // 修复表情包图片\n                            fixEmojiImages();\n                            // 添加头部和关闭按钮（如果不存在）\n                            if (!emojiPopup.querySelector(\".wl-popup-header\")) {\n                                const header = document.createElement(\"div\");\n                                header.className = \"wl-popup-header\";\n                                header.innerHTML = `\n                  <div class=\"wl-popup-title\">Choose Emoji</div>\n                  <button class=\"wl-popup-close\" type=\"button\">×</button>\n                `;\n                                emojiPopup.insertBefore(header, emojiPopup.firstChild);\n                                // 添加关闭按钮事件\n                                const closeBtn = header.querySelector(\".wl-popup-close\");\n                                if (closeBtn) {\n                                    closeBtn.addEventListener(\"click\", ()=>{\n                                        closeEmojiPopup(emojiPopup);\n                                    });\n                                }\n                            }\n                            // 添加背景点击关闭\n                            const handleBackgroundClick = (e)=>{\n                                if (e.target === emojiPopup) {\n                                    closeEmojiPopup(emojiPopup);\n                                    document.removeEventListener(\"click\", handleBackgroundClick);\n                                }\n                            };\n                            setTimeout(()=>{\n                                document.addEventListener(\"click\", handleBackgroundClick);\n                            }, 100);\n                            // 添加ESC键关闭\n                            const handleEscKey = (e)=>{\n                                if (e.key === \"Escape\") {\n                                    closeEmojiPopup(emojiPopup);\n                                    document.removeEventListener(\"keydown\", handleEscKey);\n                                }\n                            };\n                            document.addEventListener(\"keydown\", handleEscKey);\n                        }\n                    }, 50);\n                });\n            }\n        });\n        // 关闭表情包弹窗的函数\n        const closeEmojiPopup = (popup)=>{\n            popup.classList.remove(\"wl-emoji-open\", \"display\");\n            popup.setAttribute(\"style\", \"display: none !important; opacity: 0 !important; visibility: hidden !important;\");\n        };\n        // 修复GIF弹窗（如果存在）\n        const gifButtons = container.querySelectorAll(\"[data-waline] .wl-action\");\n        gifButtons.forEach((button)=>{\n            if (button.textContent?.includes(\"GIF\") || button.getAttribute(\"title\")?.includes(\"gif\")) {\n                button.addEventListener(\"click\", (e)=>{\n                    setTimeout(()=>{\n                        const gifPopup = container.querySelector(\"[data-waline] .wl-gif-popup\");\n                        if (gifPopup) {\n                            // 添加头部和关闭按钮（如果不存在）\n                            if (!gifPopup.querySelector(\".wl-popup-header\")) {\n                                const header = document.createElement(\"div\");\n                                header.className = \"wl-popup-header\";\n                                header.innerHTML = `\n                  <div class=\"wl-popup-title\">选择GIF</div>\n                  <button class=\"wl-popup-close\" type=\"button\">×</button>\n                `;\n                                gifPopup.insertBefore(header, gifPopup.firstChild);\n                                // 添加关闭按钮事件\n                                const closeBtn = header.querySelector(\".wl-popup-close\");\n                                if (closeBtn) {\n                                    closeBtn.addEventListener(\"click\", ()=>{\n                                        gifPopup.classList.remove(\"display\");\n                                    });\n                                }\n                            }\n                        }\n                    }, 50);\n                });\n            }\n        });\n    }, []);\n    // 测试表情包CDN可访问性\n    const testEmojiCDN = async ()=>{\n        const testUrls = [\n            \"https://cdn.jsdelivr.net/npm/@waline/emojis@1.2.0/weibo/info.json\",\n            \"https://unpkg.com/@waline/emojis@1.2.0/weibo/info.json\"\n        ];\n        for (const url of testUrls){\n            try {\n                const controller = new AbortController();\n                const timeoutId = setTimeout(()=>controller.abort(), 3000) // 3秒超时\n                ;\n                const response = await fetch(url, {\n                    method: \"HEAD\",\n                    signal: controller.signal\n                });\n                clearTimeout(timeoutId);\n                if (response.ok) {\n                    console.log(`表情包CDN可访问: ${url}`);\n                    return url.includes(\"cdn.jsdelivr.net\") ? \"jsdelivr\" : \"unpkg\";\n                }\n            } catch (error) {\n                console.warn(`表情包CDN不可访问: ${url}`, error);\n            }\n        }\n        console.warn(\"所有表情包CDN都不可访问，使用本地表情包\");\n        return \"local\" // 使用本地表情包\n        ;\n    };\n    // 初始化Waline的函数\n    const initWaline = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(async ()=>{\n        if (!walineRef.current) return;\n        // 取消之前的请求\n        if (abortControllerRef.current) {\n            abortControllerRef.current.abort();\n        }\n        // 创建新的AbortController\n        abortControllerRef.current = new AbortController();\n        try {\n            setIsLoading(true);\n            setError(null);\n            // 延迟初始化，避免快速切换导致的问题\n            await new Promise((resolve)=>setTimeout(resolve, 100));\n            // 检查组件是否仍然存在\n            if (!walineRef.current || abortControllerRef.current?.signal.aborted) {\n                return;\n            }\n            // 测试表情包CDN\n            const cdnProvider = await testEmojiCDN();\n            console.log(`使用表情包CDN: ${cdnProvider}`);\n            // 获取Waline配置\n            const config = await (0,_lib_walineConfig__WEBPACK_IMPORTED_MODULE_4__.getWalineConfig)();\n            // 清理容器\n            walineRef.current.innerHTML = \"\";\n            // 初始化Waline\n            const instance = (0,_waline_client__WEBPACK_IMPORTED_MODULE_1__.init)({\n                el: walineRef.current,\n                serverURL: config.serverURL,\n                path,\n                dark: resolvedTheme === \"dark\",\n                locale: {\n                    placeholder: \"Share your thoughts and join the discussion...\",\n                    admin: \"Admin\",\n                    level0: \"Newcomer\",\n                    level1: \"Explorer\",\n                    level2: \"Contributor\",\n                    level3: \"Expert\",\n                    level4: \"Master\",\n                    level5: \"Legend\",\n                    anonymous: \"Anonymous\",\n                    login: \"Sign In\",\n                    logout: \"Sign Out\",\n                    profile: \"Profile\",\n                    nickError: \"Nickname must be at least 3 characters\",\n                    mailError: \"Please enter a valid email address\",\n                    wordHint: \"Please enter your comment\",\n                    sofa: \"Be the first to share your thoughts!\",\n                    submit: \"Publish Comment\",\n                    reply: \"Reply\",\n                    cancelReply: \"Cancel Reply\",\n                    comment: \"Comment\",\n                    refresh: \"Refresh\",\n                    more: \"Load More Comments...\",\n                    preview: \"Preview\",\n                    emoji: \"Emoji\",\n                    uploadImage: \"Upload Image\",\n                    seconds: \"seconds ago\",\n                    minutes: \"minutes ago\",\n                    hours: \"hours ago\",\n                    days: \"days ago\",\n                    now: \"just now\"\n                },\n                emoji: cdnProvider === \"jsdelivr\" ? [\n                    \"https://cdn.jsdelivr.net/npm/@waline/emojis@1.2.0/alus\",\n                    \"https://cdn.jsdelivr.net/npm/@waline/emojis@1.2.0/bilibili\",\n                    \"https://cdn.jsdelivr.net/npm/@waline/emojis@1.2.0/bmoji\",\n                    \"https://cdn.jsdelivr.net/npm/@waline/emojis@1.2.0/qq\",\n                    \"https://cdn.jsdelivr.net/npm/@waline/emojis@1.2.0/tieba\",\n                    \"https://cdn.jsdelivr.net/npm/@waline/emojis@1.2.0/tw-emoji\",\n                    \"https://cdn.jsdelivr.net/npm/@waline/emojis@1.2.0/weibo\",\n                    \"https://cdn.jsdelivr.net/npm/@waline/emojis@1.2.0/soul-emoji\"\n                ] : cdnProvider === \"unpkg\" ? [\n                    \"https://unpkg.com/@waline/emojis@1.2.0/alus\",\n                    \"https://unpkg.com/@waline/emojis@1.2.0/bilibili\",\n                    \"https://unpkg.com/@waline/emojis@1.2.0/bmoji\",\n                    \"https://unpkg.com/@waline/emojis@1.2.0/qq\",\n                    \"https://unpkg.com/@waline/emojis@1.2.0/tieba\",\n                    \"https://unpkg.com/@waline/emojis@1.2.0/tw-emoji\",\n                    \"https://unpkg.com/@waline/emojis@1.2.0/weibo\",\n                    \"https://unpkg.com/@waline/emojis@1.2.0/soul-emoji\"\n                ] : [\n                    // 使用简单的Unicode表情包作为备选\n                    \"\\uD83D\\uDE00\",\n                    \"\\uD83D\\uDE03\",\n                    \"\\uD83D\\uDE04\",\n                    \"\\uD83D\\uDE01\",\n                    \"\\uD83D\\uDE06\",\n                    \"\\uD83D\\uDE05\",\n                    \"\\uD83D\\uDE02\",\n                    \"\\uD83E\\uDD23\",\n                    \"\\uD83D\\uDE0A\",\n                    \"\\uD83D\\uDE07\",\n                    \"\\uD83D\\uDE42\",\n                    \"\\uD83D\\uDE43\",\n                    \"\\uD83D\\uDE09\",\n                    \"\\uD83D\\uDE0C\",\n                    \"\\uD83D\\uDE0D\",\n                    \"\\uD83E\\uDD70\",\n                    \"\\uD83D\\uDE18\",\n                    \"\\uD83D\\uDE17\",\n                    \"\\uD83D\\uDE19\",\n                    \"\\uD83D\\uDE1A\",\n                    \"\\uD83D\\uDE0B\",\n                    \"\\uD83D\\uDE1B\",\n                    \"\\uD83D\\uDE1D\",\n                    \"\\uD83D\\uDE1C\",\n                    \"\\uD83E\\uDD2A\",\n                    \"\\uD83E\\uDD28\",\n                    \"\\uD83E\\uDDD0\",\n                    \"\\uD83E\\uDD13\",\n                    \"\\uD83D\\uDE0E\",\n                    \"\\uD83E\\uDD29\",\n                    \"\\uD83E\\uDD73\",\n                    \"\\uD83D\\uDE0F\",\n                    \"\\uD83D\\uDE12\",\n                    \"\\uD83D\\uDE1E\",\n                    \"\\uD83D\\uDE14\",\n                    \"\\uD83D\\uDE1F\",\n                    \"\\uD83D\\uDE15\",\n                    \"\\uD83D\\uDE41\",\n                    \"☹️\",\n                    \"\\uD83D\\uDE23\",\n                    \"\\uD83D\\uDE16\",\n                    \"\\uD83D\\uDE2B\",\n                    \"\\uD83D\\uDE29\",\n                    \"\\uD83E\\uDD7A\",\n                    \"\\uD83D\\uDE22\",\n                    \"\\uD83D\\uDE2D\",\n                    \"\\uD83D\\uDE24\",\n                    \"\\uD83D\\uDE20\",\n                    \"\\uD83D\\uDE21\",\n                    \"\\uD83E\\uDD2C\",\n                    \"\\uD83E\\uDD2F\",\n                    \"\\uD83D\\uDE33\",\n                    \"\\uD83E\\uDD75\",\n                    \"\\uD83E\\uDD76\",\n                    \"\\uD83D\\uDE31\",\n                    \"\\uD83D\\uDE28\",\n                    \"\\uD83D\\uDE30\",\n                    \"\\uD83D\\uDE25\",\n                    \"\\uD83D\\uDE13\",\n                    \"\\uD83E\\uDD17\",\n                    \"\\uD83E\\uDD14\",\n                    \"\\uD83E\\uDD2D\",\n                    \"\\uD83E\\uDD2B\",\n                    \"\\uD83E\\uDD25\",\n                    \"\\uD83D\\uDE36\",\n                    \"\\uD83D\\uDE10\",\n                    \"\\uD83D\\uDE11\",\n                    \"\\uD83D\\uDE2C\",\n                    \"\\uD83D\\uDE44\",\n                    \"\\uD83D\\uDE2F\",\n                    \"\\uD83D\\uDE26\",\n                    \"\\uD83D\\uDE27\",\n                    \"\\uD83D\\uDE2E\",\n                    \"\\uD83D\\uDE32\",\n                    \"\\uD83E\\uDD71\",\n                    \"\\uD83D\\uDE34\",\n                    \"\\uD83E\\uDD24\",\n                    \"\\uD83D\\uDE2A\",\n                    \"\\uD83D\\uDE35\",\n                    \"\\uD83E\\uDD10\",\n                    \"\\uD83E\\uDD74\",\n                    \"\\uD83E\\uDD22\",\n                    \"\\uD83E\\uDD2E\",\n                    \"\\uD83E\\uDD27\",\n                    \"\\uD83D\\uDE37\",\n                    \"\\uD83E\\uDD12\",\n                    \"\\uD83E\\uDD15\",\n                    \"\\uD83E\\uDD11\",\n                    \"\\uD83E\\uDD20\",\n                    \"\\uD83D\\uDE08\",\n                    \"\\uD83D\\uDC7F\",\n                    \"\\uD83D\\uDC79\",\n                    \"\\uD83D\\uDC7A\",\n                    \"\\uD83E\\uDD21\",\n                    \"\\uD83D\\uDCA9\",\n                    \"\\uD83D\\uDC7B\",\n                    \"\\uD83D\\uDC80\",\n                    \"☠️\",\n                    \"\\uD83D\\uDC7D\",\n                    \"\\uD83D\\uDC7E\",\n                    \"\\uD83E\\uDD16\",\n                    \"\\uD83C\\uDF83\",\n                    \"\\uD83D\\uDE3A\",\n                    \"\\uD83D\\uDE38\",\n                    \"\\uD83D\\uDE39\",\n                    \"\\uD83D\\uDE3B\",\n                    \"\\uD83D\\uDE3C\",\n                    \"\\uD83D\\uDE3D\",\n                    \"\\uD83D\\uDE40\",\n                    \"\\uD83D\\uDE3F\",\n                    \"\\uD83D\\uDE3E\"\n                ],\n                meta: [\n                    \"nick\",\n                    \"mail\",\n                    \"link\"\n                ],\n                requiredMeta: [\n                    \"nick\"\n                ],\n                login: \"enable\",\n                wordLimit: [\n                    0,\n                    1000\n                ],\n                pageSize: 10,\n                lang: \"en-US\",\n                reaction: true,\n                imageUploader: false,\n                texRenderer: false,\n                search: false\n            });\n            if (!abortControllerRef.current?.signal.aborted) {\n                setWalineInstance(instance);\n                setIsInitialized(true);\n                setIsLoading(false);\n                // 添加加载完成的回调和修复表情弹窗\n                setTimeout(()=>{\n                    if (walineRef.current) {\n                        walineRef.current.classList.add(\"waline-loaded\");\n                        fixEmojiPopups();\n                        enhanceAvatars();\n                        // 修复表情包图片加载\n                        const fixEmojiImages = ()=>{\n                            const emojiImages = walineRef.current?.querySelectorAll(\".wl-emoji img\");\n                            emojiImages?.forEach((img)=>{\n                                const imgElement = img;\n                                if (!imgElement.complete) {\n                                    imgElement.addEventListener(\"error\", ()=>{\n                                        console.warn(\"表情包图片加载失败:\", imgElement.src);\n                                        imgElement.style.backgroundColor = \"#f0f0f0\";\n                                        imgElement.style.border = \"1px solid #ddd\";\n                                    });\n                                    imgElement.addEventListener(\"load\", ()=>{\n                                        imgElement.style.backgroundColor = \"transparent\";\n                                        imgElement.style.border = \"none\";\n                                    });\n                                }\n                            });\n                        };\n                        fixEmojiImages();\n                        // 额外的表情包弹窗修复 - 确保所有现有的表情包弹窗都被隐藏\n                        const existingEmojiPopups = walineRef.current.querySelectorAll(\".wl-emoji\");\n                        existingEmojiPopups.forEach((popup)=>{\n                            if (!popup.classList.contains(\"wl-emoji-open\")) {\n                                popup.setAttribute(\"style\", \"display: none !important; opacity: 0 !important; visibility: hidden !important;\");\n                            }\n                        });\n                    }\n                }, 300);\n                // 监听DOM变化以应用头像增强和表情包修复\n                const observer = new MutationObserver((mutations)=>{\n                    enhanceAvatars();\n                    // 检查是否有新的表情包弹窗被创建\n                    mutations.forEach((mutation)=>{\n                        mutation.addedNodes.forEach((node)=>{\n                            if (node.nodeType === Node.ELEMENT_NODE) {\n                                const element = node;\n                                // 检查是否是表情包弹窗或包含表情包弹窗\n                                const emojiPopup = element.classList?.contains(\"wl-emoji\") ? element : element.querySelector?.(\".wl-emoji\");\n                                if (emojiPopup) {\n                                    console.log(\"检测到新的表情包弹窗，应用修复\");\n                                    // 确保新创建的表情包弹窗初始状态是隐藏的\n                                    emojiPopup.classList.remove(\"wl-emoji-open\");\n                                    emojiPopup.setAttribute(\"style\", \"display: none !important; opacity: 0 !important; visibility: hidden !important;\");\n                                    // 修复新创建的表情包图片\n                                    setTimeout(()=>{\n                                        const emojiImages = emojiPopup.querySelectorAll(\"img\");\n                                        emojiImages.forEach((img, index)=>{\n                                            const imgElement = img;\n                                            const handleLoad = ()=>{\n                                                console.log(`新表情包 ${index} 加载成功:`, imgElement.src);\n                                                imgElement.classList.remove(\"loading\", \"error\");\n                                            };\n                                            const handleError = ()=>{\n                                                console.warn(`新表情包 ${index} 加载失败:`, imgElement.src);\n                                                imgElement.classList.add(\"error\");\n                                                // 尝试备用CDN\n                                                const originalSrc = imgElement.src;\n                                                if (originalSrc.includes(\"cdn.jsdelivr.net\")) {\n                                                    const backupSrc = originalSrc.replace(\"cdn.jsdelivr.net/npm\", \"unpkg.com\");\n                                                    imgElement.src = backupSrc;\n                                                } else if (originalSrc.includes(\"unpkg.com\")) {\n                                                    const backupSrc = originalSrc.replace(\"unpkg.com\", \"cdn.jsdelivr.net/npm\");\n                                                    imgElement.src = backupSrc;\n                                                }\n                                            };\n                                            imgElement.addEventListener(\"load\", handleLoad, {\n                                                once: true\n                                            });\n                                            imgElement.addEventListener(\"error\", handleError, {\n                                                once: true\n                                            });\n                                        });\n                                    }, 100);\n                                }\n                            }\n                        });\n                    });\n                });\n                observer.observe(walineRef.current, {\n                    childList: true,\n                    subtree: true\n                });\n                // 清理函数中断开观察器\n                const originalCleanup = ()=>{\n                    observer.disconnect();\n                };\n                // 将清理函数添加到组件卸载时执行\n                return originalCleanup;\n            }\n        } catch (error) {\n            if (error instanceof Error && error.name !== \"AbortError\") {\n                console.error(\"Waline initialization error:\", error);\n                setError(\"Failed to load comments. Please refresh the page.\");\n                setIsLoading(false);\n            }\n        }\n    }, [\n        path,\n        resolvedTheme,\n        safeDestroy\n    ]);\n    // 主useEffect - 处理初始化和清理\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        setIsInitialized(false);\n        // 使用setTimeout避免在React严格模式下的双重初始化\n        initTimeoutRef.current = setTimeout(()=>{\n            initWaline();\n        }, 50);\n        return ()=>{\n            // 清理timeout\n            if (initTimeoutRef.current) {\n                clearTimeout(initTimeoutRef.current);\n            }\n            // 取消请求\n            if (abortControllerRef.current) {\n                abortControllerRef.current.abort();\n            }\n            // 安全销毁实例\n            if (walineInstance) {\n                safeDestroy(walineInstance);\n            }\n            setIsInitialized(false);\n            setWalineInstance(null);\n            setIsLoading(true);\n            setError(null);\n        };\n    }, [\n        path,\n        resolvedTheme,\n        initWaline,\n        safeDestroy\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `waline-container-premium ${className}`,\n        children: [\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6 p-4 bg-destructive/10 border border-destructive/20 rounded-xl text-destructive text-sm text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/comment/WalineComment.tsx\",\n                        lineNumber: 591,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>{\n                            setError(null);\n                            initWaline();\n                        },\n                        className: \"mt-2 px-3 py-1 bg-destructive/20 hover:bg-destructive/30 rounded-md transition-colors\",\n                        children: \"Retry\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/comment/WalineComment.tsx\",\n                        lineNumber: 592,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/comment/WalineComment.tsx\",\n                lineNumber: 590,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: walineRef,\n                className: `waline-wrapper-premium transition-all duration-500 ${isInitialized ? \"opacity-100\" : \"opacity-0\"}`,\n                style: {\n                    // 自定义CSS变量以适配设计系统\n                    \"--waline-theme-color\": \"hsl(var(--primary))\",\n                    \"--waline-active-color\": \"hsl(var(--primary))\",\n                    \"--waline-border-color\": \"hsl(var(--border))\",\n                    \"--waline-bg-color\": \"hsl(var(--background))\",\n                    \"--waline-bg-color-light\": \"hsl(var(--muted))\",\n                    \"--waline-text-color\": \"hsl(var(--foreground))\",\n                    \"--waline-light-grey\": \"hsl(var(--muted-foreground))\",\n                    \"--waline-white\": \"hsl(var(--card))\",\n                    \"--waline-color\": \"hsl(var(--foreground))\",\n                    \"--waline-border-radius\": \"0.75rem\",\n                    \"--waline-avatar-size\": \"2.5rem\",\n                    minHeight: isInitialized ? \"auto\" : \"300px\"\n                }\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/comment/WalineComment.tsx\",\n                lineNumber: 605,\n                columnNumber: 7\n            }, this),\n            isLoading && !error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col items-center justify-center py-16 space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-12 h-12 border-4 border-muted-foreground/20 border-t-primary rounded-full animate-spin\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/comment/WalineComment.tsx\",\n                                lineNumber: 631,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 w-12 h-12 border-4 border-transparent border-r-primary/50 rounded-full animate-spin\",\n                                style: {\n                                    animationDirection: \"reverse\",\n                                    animationDuration: \"0.8s\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/comment/WalineComment.tsx\",\n                                lineNumber: 632,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/comment/WalineComment.tsx\",\n                        lineNumber: 630,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center space-y-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm font-medium text-foreground\",\n                                children: \"Loading Discussion\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/comment/WalineComment.tsx\",\n                                lineNumber: 635,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs text-muted-foreground\",\n                                children: \"Preparing comment system...\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/comment/WalineComment.tsx\",\n                                lineNumber: 636,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/comment/WalineComment.tsx\",\n                        lineNumber: 634,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/comment/WalineComment.tsx\",\n                lineNumber: 629,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/comment/WalineComment.tsx\",\n        lineNumber: 587,\n        columnNumber: 5\n    }, this);\n}\n_s(WalineComment, \"h8MDnZILM1x2SbOYJYVCTJBFoc8=\", false, function() {\n    return [\n        next_themes__WEBPACK_IMPORTED_MODULE_3__.useTheme\n    ];\n});\n_c = WalineComment;\n/* harmony default export */ __webpack_exports__[\"default\"] = (WalineComment);\nvar _c;\n$RefreshReg$(_c, \"WalineComment\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/comment/WalineComment.tsx\n"));

/***/ })

});