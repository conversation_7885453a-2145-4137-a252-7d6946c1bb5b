"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/blogs/[slug]/page",{

/***/ "(app-pages-browser)/./src/components/comment/WalineComment.tsx":
/*!**************************************************!*\
  !*** ./src/components/comment/WalineComment.tsx ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WalineComment: function() { return /* binding */ WalineComment; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _waline_client__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @waline/client */ \"(app-pages-browser)/./node_modules/@waline/client/dist/slim.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-themes */ \"(app-pages-browser)/./node_modules/next-themes/dist/index.module.js\");\n/* harmony import */ var _lib_walineConfig__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/walineConfig */ \"(app-pages-browser)/./src/lib/walineConfig.ts\");\n/* __next_internal_client_entry_do_not_use__ WalineComment,default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction WalineComment({ path, title, className = \"\" }) {\n    _s();\n    const walineRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const { resolvedTheme } = (0,next_themes__WEBPACK_IMPORTED_MODULE_3__.useTheme)();\n    const [walineInstance, setWalineInstance] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [isInitialized, setIsInitialized] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const initTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const abortControllerRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    // 安全销毁实例的函数\n    const safeDestroy = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((instance)=>{\n        if (instance && typeof instance.destroy === \"function\") {\n            try {\n                instance.destroy();\n            } catch (error) {\n                console.warn(\"Waline destroy error:\", error);\n            }\n        }\n    }, []);\n    // 生成随机头像URL\n    const generateRandomAvatar = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((seed)=>{\n        const avatarStyles = [\n            \"adventurer\",\n            \"adventurer-neutral\",\n            \"avataaars\",\n            \"avataaars-neutral\",\n            \"bottts\",\n            \"bottts-neutral\",\n            \"fun-emoji\",\n            \"icons\",\n            \"identicon\",\n            \"initials\",\n            \"lorelei\",\n            \"micah\",\n            \"miniavs\",\n            \"open-peeps\",\n            \"personas\",\n            \"pixel-art\",\n            \"pixel-art-neutral\"\n        ];\n        const styleIndex = seed.split(\"\").reduce((acc, char)=>acc + char.charCodeAt(0), 0) % avatarStyles.length;\n        const selectedStyle = avatarStyles[styleIndex];\n        return `https://api.dicebear.com/9.x/${selectedStyle}/svg?seed=${encodeURIComponent(seed)}&backgroundColor=transparent&radius=50`;\n    }, []);\n    // 增强头像显示\n    const enhanceAvatars = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(()=>{\n        if (!walineRef.current) return;\n        const container = walineRef.current;\n        const avatars = container.querySelectorAll(\".wl-avatar\");\n        avatars.forEach((avatar)=>{\n            if (!avatar.classList.contains(\"wl-avatar-enhanced\")) {\n                avatar.classList.add(\"wl-avatar-enhanced\");\n                const img = avatar.querySelector(\"img\");\n                if (!img) {\n                    const commentCard = avatar.closest(\".wl-card\");\n                    const userNick = commentCard?.querySelector(\".wl-nick\")?.textContent || \"anonymous\";\n                    const userMail = commentCard?.querySelector(\".wl-mail\")?.textContent || \"\";\n                    const seed = userMail || userNick || `user-${Math.random().toString(36).substr(2, 9)}`;\n                    const avatarUrl = generateRandomAvatar(seed);\n                    const avatarImg = document.createElement(\"img\");\n                    avatarImg.src = avatarUrl;\n                    avatarImg.alt = `${userNick}'s avatar`;\n                    avatarImg.style.cssText = `\n            width: calc(100% - 4px) !important;\n            height: calc(100% - 4px) !important;\n            border-radius: 50% !important;\n            object-fit: cover !important;\n            position: absolute !important;\n            top: 2px !important;\n            left: 2px !important;\n            z-index: 1 !important;\n            transition: all 0.3s ease !important;\n          `;\n                    avatarImg.onerror = ()=>{\n                        avatar.setAttribute(\"data-no-avatar\", \"true\");\n                        avatarImg.remove();\n                    };\n                    avatar.appendChild(avatarImg);\n                    avatar.removeAttribute(\"data-no-avatar\");\n                }\n            }\n        });\n    }, [\n        generateRandomAvatar\n    ]);\n    // 修复表情弹窗定位和层级问题\n    const fixEmojiPopups = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(()=>{\n        if (!walineRef.current) return;\n        const container = walineRef.current;\n        // 修复表情包图片加载 - 增强版\n        const fixEmojiImages = ()=>{\n            const emojiImages = container.querySelectorAll(\".wl-emoji img\");\n            console.log(`找到 ${emojiImages.length} 个表情包图片`);\n            emojiImages.forEach((img, index)=>{\n                const imgElement = img;\n                // 添加加载状态\n                imgElement.classList.add(\"loading\");\n                // 检查图片是否已经加载完成\n                if (imgElement.complete && imgElement.naturalWidth > 0) {\n                    console.log(`表情包 ${index} 已加载完成:`, imgElement.src);\n                    imgElement.classList.remove(\"loading\");\n                    return;\n                }\n                // 添加加载成功处理\n                const handleLoad = ()=>{\n                    console.log(`表情包 ${index} 加载成功:`, imgElement.src);\n                    imgElement.classList.remove(\"loading\", \"error\");\n                    imgElement.style.backgroundColor = \"transparent\";\n                    imgElement.style.border = \"none\";\n                };\n                // 添加加载失败处理\n                const handleError = ()=>{\n                    console.warn(`表情包 ${index} 加载失败:`, imgElement.src);\n                    imgElement.classList.remove(\"loading\");\n                    imgElement.classList.add(\"error\");\n                    // 尝试使用备用CDN\n                    const originalSrc = imgElement.src;\n                    if (originalSrc.includes(\"cdn.jsdelivr.net\")) {\n                        const backupSrc = originalSrc.replace(\"cdn.jsdelivr.net/npm\", \"unpkg.com\");\n                        console.log(`尝试备用CDN:`, backupSrc);\n                        imgElement.src = backupSrc;\n                    } else if (originalSrc.includes(\"unpkg.com\")) {\n                        const backupSrc = originalSrc.replace(\"unpkg.com\", \"cdn.jsdelivr.net/npm\");\n                        console.log(`尝试备用CDN:`, backupSrc);\n                        imgElement.src = backupSrc;\n                    }\n                };\n                // 移除旧的事件监听器（如果存在）\n                imgElement.removeEventListener(\"load\", handleLoad);\n                imgElement.removeEventListener(\"error\", handleError);\n                // 添加新的事件监听器\n                imgElement.addEventListener(\"load\", handleLoad, {\n                    once: true\n                });\n                imgElement.addEventListener(\"error\", handleError, {\n                    once: true\n                });\n                // 如果图片src为空或无效，尝试重新设置\n                if (!imgElement.src || imgElement.src === window.location.href) {\n                    console.warn(`表情包 ${index} src无效，尝试修复`);\n                // 这里可以添加重新获取src的逻辑\n                }\n            });\n        };\n        // 修复表情弹窗 - 使用新的选择器\n        const emojiButtons = container.querySelectorAll(\"[data-waline] .wl-action\");\n        emojiButtons.forEach((button)=>{\n            if (button.textContent?.includes(\"\\uD83D\\uDE00\") || button.getAttribute(\"title\")?.includes(\"emoji\")) {\n                button.addEventListener(\"click\", (e)=>{\n                    setTimeout(()=>{\n                        // 查找表情包弹窗 - 支持多种可能的类名\n                        const emojiPopup = container.querySelector(\"[data-waline] .wl-emoji\") || container.querySelector(\"[data-waline] .wl-emoji-popup\");\n                        if (emojiPopup) {\n                            // 确保弹窗正确显示\n                            emojiPopup.classList.add(\"wl-emoji-open\");\n                            emojiPopup.style.display = \"flex\";\n                            emojiPopup.style.opacity = \"1\";\n                            emojiPopup.style.visibility = \"visible\";\n                            // 修复表情包图片\n                            fixEmojiImages();\n                            // 添加头部和关闭按钮（如果不存在）\n                            if (!emojiPopup.querySelector(\".wl-popup-header\")) {\n                                const header = document.createElement(\"div\");\n                                header.className = \"wl-popup-header\";\n                                header.innerHTML = `\n                  <div class=\"wl-popup-title\">Choose Emoji</div>\n                  <button class=\"wl-popup-close\" type=\"button\">×</button>\n                `;\n                                emojiPopup.insertBefore(header, emojiPopup.firstChild);\n                                // 添加关闭按钮事件\n                                const closeBtn = header.querySelector(\".wl-popup-close\");\n                                if (closeBtn) {\n                                    closeBtn.addEventListener(\"click\", ()=>{\n                                        closeEmojiPopup(emojiPopup);\n                                    });\n                                }\n                            }\n                            // 添加背景点击关闭\n                            const handleBackgroundClick = (e)=>{\n                                if (e.target === emojiPopup) {\n                                    closeEmojiPopup(emojiPopup);\n                                    document.removeEventListener(\"click\", handleBackgroundClick);\n                                }\n                            };\n                            setTimeout(()=>{\n                                document.addEventListener(\"click\", handleBackgroundClick);\n                            }, 100);\n                            // 添加ESC键关闭\n                            const handleEscKey = (e)=>{\n                                if (e.key === \"Escape\") {\n                                    closeEmojiPopup(emojiPopup);\n                                    document.removeEventListener(\"keydown\", handleEscKey);\n                                }\n                            };\n                            document.addEventListener(\"keydown\", handleEscKey);\n                        }\n                    }, 50);\n                });\n            }\n        });\n        // 关闭表情包弹窗的函数\n        const closeEmojiPopup = (popup)=>{\n            popup.classList.remove(\"wl-emoji-open\", \"display\");\n            popup.setAttribute(\"style\", \"display: none !important; opacity: 0 !important; visibility: hidden !important;\");\n        };\n        // 修复GIF弹窗（如果存在）\n        const gifButtons = container.querySelectorAll(\"[data-waline] .wl-action\");\n        gifButtons.forEach((button)=>{\n            if (button.textContent?.includes(\"GIF\") || button.getAttribute(\"title\")?.includes(\"gif\")) {\n                button.addEventListener(\"click\", (e)=>{\n                    setTimeout(()=>{\n                        const gifPopup = container.querySelector(\"[data-waline] .wl-gif-popup\");\n                        if (gifPopup) {\n                            // 添加头部和关闭按钮（如果不存在）\n                            if (!gifPopup.querySelector(\".wl-popup-header\")) {\n                                const header = document.createElement(\"div\");\n                                header.className = \"wl-popup-header\";\n                                header.innerHTML = `\n                  <div class=\"wl-popup-title\">选择GIF</div>\n                  <button class=\"wl-popup-close\" type=\"button\">×</button>\n                `;\n                                gifPopup.insertBefore(header, gifPopup.firstChild);\n                                // 添加关闭按钮事件\n                                const closeBtn = header.querySelector(\".wl-popup-close\");\n                                if (closeBtn) {\n                                    closeBtn.addEventListener(\"click\", ()=>{\n                                        gifPopup.classList.remove(\"display\");\n                                    });\n                                }\n                            }\n                        }\n                    }, 50);\n                });\n            }\n        });\n    }, []);\n    // 测试表情包CDN可访问性\n    const testEmojiCDN = async ()=>{\n        const testUrls = [\n            \"https://cdn.jsdelivr.net/npm/@waline/emojis@1.2.0/weibo/info.json\",\n            \"https://unpkg.com/@waline/emojis@1.2.0/weibo/info.json\"\n        ];\n        for (const url of testUrls){\n            try {\n                const controller = new AbortController();\n                const timeoutId = setTimeout(()=>controller.abort(), 3000) // 3秒超时\n                ;\n                const response = await fetch(url, {\n                    method: \"HEAD\",\n                    signal: controller.signal\n                });\n                clearTimeout(timeoutId);\n                if (response.ok) {\n                    console.log(`表情包CDN可访问: ${url}`);\n                    return url.includes(\"cdn.jsdelivr.net\") ? \"jsdelivr\" : \"unpkg\";\n                }\n            } catch (error) {\n                console.warn(`表情包CDN不可访问: ${url}`, error);\n            }\n        }\n        console.warn(\"所有表情包CDN都不可访问，使用本地表情包\");\n        return \"local\" // 使用本地表情包\n        ;\n    };\n    // 初始化Waline的函数\n    const initWaline = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(async ()=>{\n        if (!walineRef.current) return;\n        // 取消之前的请求\n        if (abortControllerRef.current) {\n            abortControllerRef.current.abort();\n        }\n        // 创建新的AbortController\n        abortControllerRef.current = new AbortController();\n        try {\n            setIsLoading(true);\n            setError(null);\n            // 延迟初始化，避免快速切换导致的问题\n            await new Promise((resolve)=>setTimeout(resolve, 100));\n            // 检查组件是否仍然存在\n            if (!walineRef.current || abortControllerRef.current?.signal.aborted) {\n                return;\n            }\n            // 测试表情包CDN\n            const cdnProvider = await testEmojiCDN();\n            console.log(`使用表情包CDN: ${cdnProvider}`);\n            // 获取Waline配置\n            const config = await (0,_lib_walineConfig__WEBPACK_IMPORTED_MODULE_4__.getWalineConfig)();\n            // 清理容器\n            walineRef.current.innerHTML = \"\";\n            // 初始化Waline\n            const instance = (0,_waline_client__WEBPACK_IMPORTED_MODULE_1__.init)({\n                el: walineRef.current,\n                serverURL: config.serverURL,\n                path,\n                dark: resolvedTheme === \"dark\",\n                locale: {\n                    placeholder: \"Share your thoughts and join the discussion...\",\n                    admin: \"Admin\",\n                    level0: \"Newcomer\",\n                    level1: \"Explorer\",\n                    level2: \"Contributor\",\n                    level3: \"Expert\",\n                    level4: \"Master\",\n                    level5: \"Legend\",\n                    anonymous: \"Anonymous\",\n                    login: \"Sign In\",\n                    logout: \"Sign Out\",\n                    profile: \"Profile\",\n                    nickError: \"Nickname must be at least 3 characters\",\n                    mailError: \"Please enter a valid email address\",\n                    wordHint: \"Please enter your comment\",\n                    sofa: \"Be the first to share your thoughts!\",\n                    submit: \"Publish Comment\",\n                    reply: \"Reply\",\n                    cancelReply: \"Cancel Reply\",\n                    comment: \"Comment\",\n                    refresh: \"Refresh\",\n                    more: \"Load More Comments...\",\n                    preview: \"Preview\",\n                    emoji: \"Emoji\",\n                    uploadImage: \"Upload Image\",\n                    seconds: \"seconds ago\",\n                    minutes: \"minutes ago\",\n                    hours: \"hours ago\",\n                    days: \"days ago\",\n                    now: \"just now\"\n                },\n                emoji: cdnProvider === \"jsdelivr\" ? [\n                    \"https://cdn.jsdelivr.net/npm/@waline/emojis@1.2.0/alus\",\n                    \"https://cdn.jsdelivr.net/npm/@waline/emojis@1.2.0/bilibili\",\n                    \"https://cdn.jsdelivr.net/npm/@waline/emojis@1.2.0/bmoji\",\n                    \"https://cdn.jsdelivr.net/npm/@waline/emojis@1.2.0/qq\",\n                    \"https://cdn.jsdelivr.net/npm/@waline/emojis@1.2.0/tieba\",\n                    \"https://cdn.jsdelivr.net/npm/@waline/emojis@1.2.0/tw-emoji\",\n                    \"https://cdn.jsdelivr.net/npm/@waline/emojis@1.2.0/weibo\",\n                    \"https://cdn.jsdelivr.net/npm/@waline/emojis@1.2.0/soul-emoji\"\n                ] : [\n                    \"https://unpkg.com/@waline/emojis@1.2.0/alus\",\n                    \"https://unpkg.com/@waline/emojis@1.2.0/bilibili\",\n                    \"https://unpkg.com/@waline/emojis@1.2.0/bmoji\",\n                    \"https://unpkg.com/@waline/emojis@1.2.0/qq\",\n                    \"https://unpkg.com/@waline/emojis@1.2.0/tieba\",\n                    \"https://unpkg.com/@waline/emojis@1.2.0/tw-emoji\",\n                    \"https://unpkg.com/@waline/emojis@1.2.0/weibo\",\n                    \"https://unpkg.com/@waline/emojis@1.2.0/soul-emoji\"\n                ],\n                meta: [\n                    \"nick\",\n                    \"mail\",\n                    \"link\"\n                ],\n                requiredMeta: [\n                    \"nick\"\n                ],\n                login: \"enable\",\n                wordLimit: [\n                    0,\n                    1000\n                ],\n                pageSize: 10,\n                lang: \"en-US\",\n                reaction: true,\n                imageUploader: false,\n                texRenderer: false,\n                search: false\n            });\n            if (!abortControllerRef.current?.signal.aborted) {\n                setWalineInstance(instance);\n                setIsInitialized(true);\n                setIsLoading(false);\n                // 添加加载完成的回调和修复表情弹窗\n                setTimeout(()=>{\n                    if (walineRef.current) {\n                        walineRef.current.classList.add(\"waline-loaded\");\n                        fixEmojiPopups();\n                        enhanceAvatars();\n                        // 修复表情包图片加载\n                        const fixEmojiImages = ()=>{\n                            const emojiImages = walineRef.current?.querySelectorAll(\".wl-emoji img\");\n                            emojiImages?.forEach((img)=>{\n                                const imgElement = img;\n                                if (!imgElement.complete) {\n                                    imgElement.addEventListener(\"error\", ()=>{\n                                        console.warn(\"表情包图片加载失败:\", imgElement.src);\n                                        imgElement.style.backgroundColor = \"#f0f0f0\";\n                                        imgElement.style.border = \"1px solid #ddd\";\n                                    });\n                                    imgElement.addEventListener(\"load\", ()=>{\n                                        imgElement.style.backgroundColor = \"transparent\";\n                                        imgElement.style.border = \"none\";\n                                    });\n                                }\n                            });\n                        };\n                        fixEmojiImages();\n                        // 额外的表情包弹窗修复 - 确保所有现有的表情包弹窗都被隐藏\n                        const existingEmojiPopups = walineRef.current.querySelectorAll(\".wl-emoji\");\n                        existingEmojiPopups.forEach((popup)=>{\n                            if (!popup.classList.contains(\"wl-emoji-open\")) {\n                                popup.setAttribute(\"style\", \"display: none !important; opacity: 0 !important; visibility: hidden !important;\");\n                            }\n                        });\n                    }\n                }, 300);\n                // 监听DOM变化以应用头像增强和表情包修复\n                const observer = new MutationObserver((mutations)=>{\n                    enhanceAvatars();\n                    // 检查是否有新的表情包弹窗被创建\n                    mutations.forEach((mutation)=>{\n                        mutation.addedNodes.forEach((node)=>{\n                            if (node.nodeType === Node.ELEMENT_NODE) {\n                                const element = node;\n                                // 检查是否是表情包弹窗或包含表情包弹窗\n                                const emojiPopup = element.classList?.contains(\"wl-emoji\") ? element : element.querySelector?.(\".wl-emoji\");\n                                if (emojiPopup) {\n                                    console.log(\"检测到新的表情包弹窗，应用修复\");\n                                    // 确保新创建的表情包弹窗初始状态是隐藏的\n                                    emojiPopup.classList.remove(\"wl-emoji-open\");\n                                    emojiPopup.setAttribute(\"style\", \"display: none !important; opacity: 0 !important; visibility: hidden !important;\");\n                                    // 修复新创建的表情包图片\n                                    setTimeout(()=>{\n                                        const emojiImages = emojiPopup.querySelectorAll(\"img\");\n                                        emojiImages.forEach((img, index)=>{\n                                            const imgElement = img;\n                                            const handleLoad = ()=>{\n                                                console.log(`新表情包 ${index} 加载成功:`, imgElement.src);\n                                                imgElement.classList.remove(\"loading\", \"error\");\n                                            };\n                                            const handleError = ()=>{\n                                                console.warn(`新表情包 ${index} 加载失败:`, imgElement.src);\n                                                imgElement.classList.add(\"error\");\n                                                // 尝试备用CDN\n                                                const originalSrc = imgElement.src;\n                                                if (originalSrc.includes(\"cdn.jsdelivr.net\")) {\n                                                    const backupSrc = originalSrc.replace(\"cdn.jsdelivr.net/npm\", \"unpkg.com\");\n                                                    imgElement.src = backupSrc;\n                                                } else if (originalSrc.includes(\"unpkg.com\")) {\n                                                    const backupSrc = originalSrc.replace(\"unpkg.com\", \"cdn.jsdelivr.net/npm\");\n                                                    imgElement.src = backupSrc;\n                                                }\n                                            };\n                                            imgElement.addEventListener(\"load\", handleLoad, {\n                                                once: true\n                                            });\n                                            imgElement.addEventListener(\"error\", handleError, {\n                                                once: true\n                                            });\n                                        });\n                                    }, 100);\n                                }\n                            }\n                        });\n                    });\n                });\n                observer.observe(walineRef.current, {\n                    childList: true,\n                    subtree: true\n                });\n                // 清理函数中断开观察器\n                const originalCleanup = ()=>{\n                    observer.disconnect();\n                };\n                // 将清理函数添加到组件卸载时执行\n                return originalCleanup;\n            }\n        } catch (error) {\n            if (error instanceof Error && error.name !== \"AbortError\") {\n                console.error(\"Waline initialization error:\", error);\n                setError(\"Failed to load comments. Please refresh the page.\");\n                setIsLoading(false);\n            }\n        }\n    }, [\n        path,\n        resolvedTheme,\n        safeDestroy\n    ]);\n    // 主useEffect - 处理初始化和清理\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        setIsInitialized(false);\n        // 使用setTimeout避免在React严格模式下的双重初始化\n        initTimeoutRef.current = setTimeout(()=>{\n            initWaline();\n        }, 50);\n        return ()=>{\n            // 清理timeout\n            if (initTimeoutRef.current) {\n                clearTimeout(initTimeoutRef.current);\n            }\n            // 取消请求\n            if (abortControllerRef.current) {\n                abortControllerRef.current.abort();\n            }\n            // 安全销毁实例\n            if (walineInstance) {\n                safeDestroy(walineInstance);\n            }\n            setIsInitialized(false);\n            setWalineInstance(null);\n            setIsLoading(true);\n            setError(null);\n        };\n    }, [\n        path,\n        resolvedTheme,\n        initWaline,\n        safeDestroy\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `waline-container-premium ${className}`,\n        children: [\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6 p-4 bg-destructive/10 border border-destructive/20 rounded-xl text-destructive text-sm text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/comment/WalineComment.tsx\",\n                        lineNumber: 575,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>{\n                            setError(null);\n                            initWaline();\n                        },\n                        className: \"mt-2 px-3 py-1 bg-destructive/20 hover:bg-destructive/30 rounded-md transition-colors\",\n                        children: \"Retry\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/comment/WalineComment.tsx\",\n                        lineNumber: 576,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/comment/WalineComment.tsx\",\n                lineNumber: 574,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: walineRef,\n                className: `waline-wrapper-premium transition-all duration-500 ${isInitialized ? \"opacity-100\" : \"opacity-0\"}`,\n                style: {\n                    // 自定义CSS变量以适配设计系统\n                    \"--waline-theme-color\": \"hsl(var(--primary))\",\n                    \"--waline-active-color\": \"hsl(var(--primary))\",\n                    \"--waline-border-color\": \"hsl(var(--border))\",\n                    \"--waline-bg-color\": \"hsl(var(--background))\",\n                    \"--waline-bg-color-light\": \"hsl(var(--muted))\",\n                    \"--waline-text-color\": \"hsl(var(--foreground))\",\n                    \"--waline-light-grey\": \"hsl(var(--muted-foreground))\",\n                    \"--waline-white\": \"hsl(var(--card))\",\n                    \"--waline-color\": \"hsl(var(--foreground))\",\n                    \"--waline-border-radius\": \"0.75rem\",\n                    \"--waline-avatar-size\": \"2.5rem\",\n                    minHeight: isInitialized ? \"auto\" : \"300px\"\n                }\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/comment/WalineComment.tsx\",\n                lineNumber: 589,\n                columnNumber: 7\n            }, this),\n            isLoading && !error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col items-center justify-center py-16 space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-12 h-12 border-4 border-muted-foreground/20 border-t-primary rounded-full animate-spin\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/comment/WalineComment.tsx\",\n                                lineNumber: 615,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 w-12 h-12 border-4 border-transparent border-r-primary/50 rounded-full animate-spin\",\n                                style: {\n                                    animationDirection: \"reverse\",\n                                    animationDuration: \"0.8s\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/comment/WalineComment.tsx\",\n                                lineNumber: 616,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/comment/WalineComment.tsx\",\n                        lineNumber: 614,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center space-y-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm font-medium text-foreground\",\n                                children: \"Loading Discussion\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/comment/WalineComment.tsx\",\n                                lineNumber: 619,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs text-muted-foreground\",\n                                children: \"Preparing comment system...\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/comment/WalineComment.tsx\",\n                                lineNumber: 620,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/comment/WalineComment.tsx\",\n                        lineNumber: 618,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/comment/WalineComment.tsx\",\n                lineNumber: 613,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/comment/WalineComment.tsx\",\n        lineNumber: 571,\n        columnNumber: 5\n    }, this);\n}\n_s(WalineComment, \"h8MDnZILM1x2SbOYJYVCTJBFoc8=\", false, function() {\n    return [\n        next_themes__WEBPACK_IMPORTED_MODULE_3__.useTheme\n    ];\n});\n_c = WalineComment;\n/* harmony default export */ __webpack_exports__[\"default\"] = (WalineComment);\nvar _c;\n$RefreshReg$(_c, \"WalineComment\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL2NvbW1lbnQvV2FsaW5lQ29tbWVudC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBRXFDO0FBQzJCO0FBQzFCO0FBQ2M7QUFRN0MsU0FBU08sY0FBYyxFQUFFQyxJQUFJLEVBQUVDLEtBQUssRUFBRUMsWUFBWSxFQUFFLEVBQXNCOztJQUMvRSxNQUFNQyxZQUFZVCw2Q0FBTUEsQ0FBaUI7SUFDekMsTUFBTSxFQUFFVSxhQUFhLEVBQUUsR0FBR1AscURBQVFBO0lBQ2xDLE1BQU0sQ0FBQ1EsZ0JBQWdCQyxrQkFBa0IsR0FBR1gsK0NBQVFBLENBQU07SUFDMUQsTUFBTSxDQUFDWSxlQUFlQyxpQkFBaUIsR0FBR2IsK0NBQVFBLENBQUM7SUFDbkQsTUFBTSxDQUFDYyxXQUFXQyxhQUFhLEdBQUdmLCtDQUFRQSxDQUFDO0lBQzNDLE1BQU0sQ0FBQ2dCLE9BQU9DLFNBQVMsR0FBR2pCLCtDQUFRQSxDQUFnQjtJQUNsRCxNQUFNa0IsaUJBQWlCbkIsNkNBQU1BLENBQXdCO0lBQ3JELE1BQU1vQixxQkFBcUJwQiw2Q0FBTUEsQ0FBeUI7SUFFMUQsWUFBWTtJQUNaLE1BQU1xQixjQUFjbkIsa0RBQVdBLENBQUMsQ0FBQ29CO1FBQy9CLElBQUlBLFlBQVksT0FBT0EsU0FBU0MsT0FBTyxLQUFLLFlBQVk7WUFDdEQsSUFBSTtnQkFDRkQsU0FBU0MsT0FBTztZQUNsQixFQUFFLE9BQU9OLE9BQU87Z0JBQ2RPLFFBQVFDLElBQUksQ0FBQyx5QkFBeUJSO1lBQ3hDO1FBQ0Y7SUFDRixHQUFHLEVBQUU7SUFFTCxZQUFZO0lBQ1osTUFBTVMsdUJBQXVCeEIsa0RBQVdBLENBQUMsQ0FBQ3lCO1FBQ3hDLE1BQU1DLGVBQWU7WUFDbkI7WUFDQTtZQUNBO1lBQ0E7WUFDQTtZQUNBO1lBQ0E7WUFDQTtZQUNBO1lBQ0E7WUFDQTtZQUNBO1lBQ0E7WUFDQTtZQUNBO1lBQ0E7WUFDQTtTQUNEO1FBRUQsTUFBTUMsYUFBYUYsS0FBS0csS0FBSyxDQUFDLElBQUlDLE1BQU0sQ0FBQyxDQUFDQyxLQUFLQyxPQUFTRCxNQUFNQyxLQUFLQyxVQUFVLENBQUMsSUFBSSxLQUFLTixhQUFhTyxNQUFNO1FBQzFHLE1BQU1DLGdCQUFnQlIsWUFBWSxDQUFDQyxXQUFXO1FBRTlDLE9BQU8sQ0FBQyw2QkFBNkIsRUFBRU8sY0FBYyxVQUFVLEVBQUVDLG1CQUFtQlYsTUFBTSxzQ0FBc0MsQ0FBQztJQUNuSSxHQUFHLEVBQUU7SUFFTCxTQUFTO0lBQ1QsTUFBTVcsaUJBQWlCcEMsa0RBQVdBLENBQUM7UUFDakMsSUFBSSxDQUFDTyxVQUFVOEIsT0FBTyxFQUFFO1FBRXhCLE1BQU1DLFlBQVkvQixVQUFVOEIsT0FBTztRQUNuQyxNQUFNRSxVQUFVRCxVQUFVRSxnQkFBZ0IsQ0FBQztRQUUzQ0QsUUFBUUUsT0FBTyxDQUFDQyxDQUFBQTtZQUNkLElBQUksQ0FBQ0EsT0FBT0MsU0FBUyxDQUFDQyxRQUFRLENBQUMsdUJBQXVCO2dCQUNwREYsT0FBT0MsU0FBUyxDQUFDRSxHQUFHLENBQUM7Z0JBRXJCLE1BQU1DLE1BQU1KLE9BQU9LLGFBQWEsQ0FBQztnQkFDakMsSUFBSSxDQUFDRCxLQUFLO29CQUNSLE1BQU1FLGNBQWNOLE9BQU9PLE9BQU8sQ0FBQztvQkFDbkMsTUFBTUMsV0FBV0YsYUFBYUQsY0FBYyxhQUFhSSxlQUFlO29CQUN4RSxNQUFNQyxXQUFXSixhQUFhRCxjQUFjLGFBQWFJLGVBQWU7b0JBRXhFLE1BQU0xQixPQUFPMkIsWUFBWUYsWUFBWSxDQUFDLEtBQUssRUFBRUcsS0FBS0MsTUFBTSxHQUFHQyxRQUFRLENBQUMsSUFBSUMsTUFBTSxDQUFDLEdBQUcsR0FBRyxDQUFDO29CQUN0RixNQUFNQyxZQUFZakMscUJBQXFCQztvQkFFdkMsTUFBTWlDLFlBQVlDLFNBQVNDLGFBQWEsQ0FBQztvQkFDekNGLFVBQVVHLEdBQUcsR0FBR0o7b0JBQ2hCQyxVQUFVSSxHQUFHLEdBQUcsQ0FBQyxFQUFFWixTQUFTLFNBQVMsQ0FBQztvQkFDdENRLFVBQVVLLEtBQUssQ0FBQ0MsT0FBTyxHQUFHLENBQUM7Ozs7Ozs7Ozs7VUFVM0IsQ0FBQztvQkFFRE4sVUFBVU8sT0FBTyxHQUFHO3dCQUNsQnZCLE9BQU93QixZQUFZLENBQUMsa0JBQWtCO3dCQUN0Q1IsVUFBVVMsTUFBTTtvQkFDbEI7b0JBRUF6QixPQUFPMEIsV0FBVyxDQUFDVjtvQkFDbkJoQixPQUFPMkIsZUFBZSxDQUFDO2dCQUN6QjtZQUNGO1FBQ0Y7SUFDRixHQUFHO1FBQUM3QztLQUFxQjtJQUV6QixnQkFBZ0I7SUFDaEIsTUFBTThDLGlCQUFpQnRFLGtEQUFXQSxDQUFDO1FBQ2pDLElBQUksQ0FBQ08sVUFBVThCLE9BQU8sRUFBRTtRQUV4QixNQUFNQyxZQUFZL0IsVUFBVThCLE9BQU87UUFFbkMsa0JBQWtCO1FBQ2xCLE1BQU1rQyxpQkFBaUI7WUFDckIsTUFBTUMsY0FBY2xDLFVBQVVFLGdCQUFnQixDQUFDO1lBQy9DbEIsUUFBUW1ELEdBQUcsQ0FBQyxDQUFDLEdBQUcsRUFBRUQsWUFBWXZDLE1BQU0sQ0FBQyxPQUFPLENBQUM7WUFFN0N1QyxZQUFZL0IsT0FBTyxDQUFDLENBQUNLLEtBQWM0QjtnQkFDakMsTUFBTUMsYUFBYTdCO2dCQUVuQixTQUFTO2dCQUNUNkIsV0FBV2hDLFNBQVMsQ0FBQ0UsR0FBRyxDQUFDO2dCQUV6QixlQUFlO2dCQUNmLElBQUk4QixXQUFXQyxRQUFRLElBQUlELFdBQVdFLFlBQVksR0FBRyxHQUFHO29CQUN0RHZELFFBQVFtRCxHQUFHLENBQUMsQ0FBQyxJQUFJLEVBQUVDLE1BQU0sT0FBTyxDQUFDLEVBQUVDLFdBQVdkLEdBQUc7b0JBQ2pEYyxXQUFXaEMsU0FBUyxDQUFDd0IsTUFBTSxDQUFDO29CQUM1QjtnQkFDRjtnQkFFQSxXQUFXO2dCQUNYLE1BQU1XLGFBQWE7b0JBQ2pCeEQsUUFBUW1ELEdBQUcsQ0FBQyxDQUFDLElBQUksRUFBRUMsTUFBTSxNQUFNLENBQUMsRUFBRUMsV0FBV2QsR0FBRztvQkFDaERjLFdBQVdoQyxTQUFTLENBQUN3QixNQUFNLENBQUMsV0FBVztvQkFDdkNRLFdBQVdaLEtBQUssQ0FBQ2dCLGVBQWUsR0FBRztvQkFDbkNKLFdBQVdaLEtBQUssQ0FBQ2lCLE1BQU0sR0FBRztnQkFDNUI7Z0JBRUEsV0FBVztnQkFDWCxNQUFNQyxjQUFjO29CQUNsQjNELFFBQVFDLElBQUksQ0FBQyxDQUFDLElBQUksRUFBRW1ELE1BQU0sTUFBTSxDQUFDLEVBQUVDLFdBQVdkLEdBQUc7b0JBQ2pEYyxXQUFXaEMsU0FBUyxDQUFDd0IsTUFBTSxDQUFDO29CQUM1QlEsV0FBV2hDLFNBQVMsQ0FBQ0UsR0FBRyxDQUFDO29CQUV6QixZQUFZO29CQUNaLE1BQU1xQyxjQUFjUCxXQUFXZCxHQUFHO29CQUNsQyxJQUFJcUIsWUFBWUMsUUFBUSxDQUFDLHFCQUFxQjt3QkFDNUMsTUFBTUMsWUFBWUYsWUFBWUcsT0FBTyxDQUFDLHdCQUF3Qjt3QkFDOUQvRCxRQUFRbUQsR0FBRyxDQUFDLENBQUMsUUFBUSxDQUFDLEVBQUVXO3dCQUN4QlQsV0FBV2QsR0FBRyxHQUFHdUI7b0JBQ25CLE9BQU8sSUFBSUYsWUFBWUMsUUFBUSxDQUFDLGNBQWM7d0JBQzVDLE1BQU1DLFlBQVlGLFlBQVlHLE9BQU8sQ0FBQyxhQUFhO3dCQUNuRC9ELFFBQVFtRCxHQUFHLENBQUMsQ0FBQyxRQUFRLENBQUMsRUFBRVc7d0JBQ3hCVCxXQUFXZCxHQUFHLEdBQUd1QjtvQkFDbkI7Z0JBQ0Y7Z0JBRUEsa0JBQWtCO2dCQUNsQlQsV0FBV1csbUJBQW1CLENBQUMsUUFBUVI7Z0JBQ3ZDSCxXQUFXVyxtQkFBbUIsQ0FBQyxTQUFTTDtnQkFFeEMsWUFBWTtnQkFDWk4sV0FBV1ksZ0JBQWdCLENBQUMsUUFBUVQsWUFBWTtvQkFBRVUsTUFBTTtnQkFBSztnQkFDN0RiLFdBQVdZLGdCQUFnQixDQUFDLFNBQVNOLGFBQWE7b0JBQUVPLE1BQU07Z0JBQUs7Z0JBRS9ELHNCQUFzQjtnQkFDdEIsSUFBSSxDQUFDYixXQUFXZCxHQUFHLElBQUljLFdBQVdkLEdBQUcsS0FBSzRCLE9BQU9DLFFBQVEsQ0FBQ0MsSUFBSSxFQUFFO29CQUM5RHJFLFFBQVFDLElBQUksQ0FBQyxDQUFDLElBQUksRUFBRW1ELE1BQU0sV0FBVyxDQUFDO2dCQUN0QyxtQkFBbUI7Z0JBQ3JCO1lBQ0Y7UUFDRjtRQUVBLG1CQUFtQjtRQUNuQixNQUFNa0IsZUFBZXRELFVBQVVFLGdCQUFnQixDQUFDO1FBQ2hEb0QsYUFBYW5ELE9BQU8sQ0FBQyxDQUFDb0Q7WUFDcEIsSUFBSUEsT0FBTzFDLFdBQVcsRUFBRWdDLFNBQVMsbUJBQVNVLE9BQU9DLFlBQVksQ0FBQyxVQUFVWCxTQUFTLFVBQVU7Z0JBQ3pGVSxPQUFPTixnQkFBZ0IsQ0FBQyxTQUFTLENBQUNRO29CQUNoQ0MsV0FBVzt3QkFDVCxzQkFBc0I7d0JBQ3RCLE1BQU1DLGFBQWEzRCxVQUFVUyxhQUFhLENBQUMsOEJBQ3pCVCxVQUFVUyxhQUFhLENBQUM7d0JBRTFDLElBQUlrRCxZQUFZOzRCQUNkLFdBQVc7NEJBQ1hBLFdBQVd0RCxTQUFTLENBQUNFLEdBQUcsQ0FBQzs0QkFDekJvRCxXQUFXbEMsS0FBSyxDQUFDbUMsT0FBTyxHQUFHOzRCQUMzQkQsV0FBV2xDLEtBQUssQ0FBQ29DLE9BQU8sR0FBRzs0QkFDM0JGLFdBQVdsQyxLQUFLLENBQUNxQyxVQUFVLEdBQUc7NEJBRTlCLFVBQVU7NEJBQ1Y3Qjs0QkFFQSxtQkFBbUI7NEJBQ25CLElBQUksQ0FBQzBCLFdBQVdsRCxhQUFhLENBQUMscUJBQXFCO2dDQUNqRCxNQUFNc0QsU0FBUzFDLFNBQVNDLGFBQWEsQ0FBQztnQ0FDdEN5QyxPQUFPL0YsU0FBUyxHQUFHO2dDQUNuQitGLE9BQU9DLFNBQVMsR0FBRyxDQUFDOzs7Z0JBR3BCLENBQUM7Z0NBQ0RMLFdBQVdNLFlBQVksQ0FBQ0YsUUFBUUosV0FBV08sVUFBVTtnQ0FFckQsV0FBVztnQ0FDWCxNQUFNQyxXQUFXSixPQUFPdEQsYUFBYSxDQUFDO2dDQUN0QyxJQUFJMEQsVUFBVTtvQ0FDWkEsU0FBU2xCLGdCQUFnQixDQUFDLFNBQVM7d0NBQ2pDbUIsZ0JBQWdCVDtvQ0FDbEI7Z0NBQ0Y7NEJBQ0Y7NEJBRUEsV0FBVzs0QkFDWCxNQUFNVSx3QkFBd0IsQ0FBQ1o7Z0NBQzdCLElBQUlBLEVBQUVhLE1BQU0sS0FBS1gsWUFBWTtvQ0FDM0JTLGdCQUFnQlQ7b0NBQ2hCdEMsU0FBUzJCLG1CQUFtQixDQUFDLFNBQVNxQjtnQ0FDeEM7NEJBQ0Y7NEJBRUFYLFdBQVc7Z0NBQ1RyQyxTQUFTNEIsZ0JBQWdCLENBQUMsU0FBU29COzRCQUNyQyxHQUFHOzRCQUVILFdBQVc7NEJBQ1gsTUFBTUUsZUFBZSxDQUFDZDtnQ0FDcEIsSUFBSUEsRUFBRWUsR0FBRyxLQUFLLFVBQVU7b0NBQ3RCSixnQkFBZ0JUO29DQUNoQnRDLFNBQVMyQixtQkFBbUIsQ0FBQyxXQUFXdUI7Z0NBQzFDOzRCQUNGOzRCQUNBbEQsU0FBUzRCLGdCQUFnQixDQUFDLFdBQVdzQjt3QkFDdkM7b0JBQ0YsR0FBRztnQkFDTDtZQUNGO1FBQ0Y7UUFFQSxhQUFhO1FBQ2IsTUFBTUgsa0JBQWtCLENBQUNLO1lBQ3ZCQSxNQUFNcEUsU0FBUyxDQUFDd0IsTUFBTSxDQUFDLGlCQUFpQjtZQUN4QzRDLE1BQU03QyxZQUFZLENBQUMsU0FBUztRQUM5QjtRQUVBLGdCQUFnQjtRQUNoQixNQUFNOEMsYUFBYTFFLFVBQVVFLGdCQUFnQixDQUFDO1FBQzlDd0UsV0FBV3ZFLE9BQU8sQ0FBQyxDQUFDb0Q7WUFDbEIsSUFBSUEsT0FBTzFDLFdBQVcsRUFBRWdDLFNBQVMsVUFBVVUsT0FBT0MsWUFBWSxDQUFDLFVBQVVYLFNBQVMsUUFBUTtnQkFDeEZVLE9BQU9OLGdCQUFnQixDQUFDLFNBQVMsQ0FBQ1E7b0JBQ2hDQyxXQUFXO3dCQUNULE1BQU1pQixXQUFXM0UsVUFBVVMsYUFBYSxDQUFDO3dCQUN6QyxJQUFJa0UsVUFBVTs0QkFDWixtQkFBbUI7NEJBQ25CLElBQUksQ0FBQ0EsU0FBU2xFLGFBQWEsQ0FBQyxxQkFBcUI7Z0NBQy9DLE1BQU1zRCxTQUFTMUMsU0FBU0MsYUFBYSxDQUFDO2dDQUN0Q3lDLE9BQU8vRixTQUFTLEdBQUc7Z0NBQ25CK0YsT0FBT0MsU0FBUyxHQUFHLENBQUM7OztnQkFHcEIsQ0FBQztnQ0FDRFcsU0FBU1YsWUFBWSxDQUFDRixRQUFRWSxTQUFTVCxVQUFVO2dDQUVqRCxXQUFXO2dDQUNYLE1BQU1DLFdBQVdKLE9BQU90RCxhQUFhLENBQUM7Z0NBQ3RDLElBQUkwRCxVQUFVO29DQUNaQSxTQUFTbEIsZ0JBQWdCLENBQUMsU0FBUzt3Q0FDakMwQixTQUFTdEUsU0FBUyxDQUFDd0IsTUFBTSxDQUFDO29DQUM1QjtnQ0FDRjs0QkFDRjt3QkFDRjtvQkFDRixHQUFHO2dCQUNMO1lBQ0Y7UUFDRjtJQUNGLEdBQUcsRUFBRTtJQUVMLGVBQWU7SUFDZixNQUFNK0MsZUFBZTtRQUNuQixNQUFNQyxXQUFXO1lBQ2Y7WUFDQTtTQUNEO1FBRUQsS0FBSyxNQUFNQyxPQUFPRCxTQUFVO1lBQzFCLElBQUk7Z0JBQ0YsTUFBTUUsYUFBYSxJQUFJQztnQkFDdkIsTUFBTUMsWUFBWXZCLFdBQVcsSUFBTXFCLFdBQVdHLEtBQUssSUFBSSxNQUFNLE9BQU87O2dCQUVwRSxNQUFNQyxXQUFXLE1BQU1DLE1BQU1OLEtBQUs7b0JBQ2hDTyxRQUFRO29CQUNSQyxRQUFRUCxXQUFXTyxNQUFNO2dCQUMzQjtnQkFFQUMsYUFBYU47Z0JBRWIsSUFBSUUsU0FBU0ssRUFBRSxFQUFFO29CQUNmeEcsUUFBUW1ELEdBQUcsQ0FBQyxDQUFDLFdBQVcsRUFBRTJDLElBQUksQ0FBQztvQkFDL0IsT0FBT0EsSUFBSWpDLFFBQVEsQ0FBQyxzQkFBc0IsYUFBYTtnQkFDekQ7WUFDRixFQUFFLE9BQU9wRSxPQUFPO2dCQUNkTyxRQUFRQyxJQUFJLENBQUMsQ0FBQyxZQUFZLEVBQUU2RixJQUFJLENBQUMsRUFBRXJHO1lBQ3JDO1FBQ0Y7UUFFQU8sUUFBUUMsSUFBSSxDQUFDO1FBQ2IsT0FBTyxRQUFRLFVBQVU7O0lBQzNCO0lBRUEsZUFBZTtJQUNmLE1BQU13RyxhQUFhL0gsa0RBQVdBLENBQUM7UUFDN0IsSUFBSSxDQUFDTyxVQUFVOEIsT0FBTyxFQUFFO1FBRXhCLFVBQVU7UUFDVixJQUFJbkIsbUJBQW1CbUIsT0FBTyxFQUFFO1lBQzlCbkIsbUJBQW1CbUIsT0FBTyxDQUFDbUYsS0FBSztRQUNsQztRQUVBLHNCQUFzQjtRQUN0QnRHLG1CQUFtQm1CLE9BQU8sR0FBRyxJQUFJaUY7UUFFakMsSUFBSTtZQUNGeEcsYUFBYTtZQUNiRSxTQUFTO1lBRVQsb0JBQW9CO1lBQ3BCLE1BQU0sSUFBSWdILFFBQVFDLENBQUFBLFVBQVdqQyxXQUFXaUMsU0FBUztZQUVqRCxhQUFhO1lBQ2IsSUFBSSxDQUFDMUgsVUFBVThCLE9BQU8sSUFBSW5CLG1CQUFtQm1CLE9BQU8sRUFBRXVGLE9BQU9NLFNBQVM7Z0JBQ3BFO1lBQ0Y7WUFFQSxXQUFXO1lBQ1gsTUFBTUMsY0FBYyxNQUFNakI7WUFDMUI1RixRQUFRbUQsR0FBRyxDQUFDLENBQUMsVUFBVSxFQUFFMEQsWUFBWSxDQUFDO1lBRXRDLGFBQWE7WUFDYixNQUFNQyxTQUFTLE1BQU1sSSxrRUFBZUE7WUFFcEMsT0FBTztZQUNQSyxVQUFVOEIsT0FBTyxDQUFDaUUsU0FBUyxHQUFHO1lBRTlCLFlBQVk7WUFDWixNQUFNbEYsV0FBV3hCLG9EQUFJQSxDQUFDO2dCQUNwQnlJLElBQUk5SCxVQUFVOEIsT0FBTztnQkFDckJpRyxXQUFXRixPQUFPRSxTQUFTO2dCQUMzQmxJO2dCQUNBbUksTUFBTS9ILGtCQUFrQjtnQkFDeEJnSSxRQUFRO29CQUNOQyxhQUFhO29CQUNiQyxPQUFPO29CQUNQQyxRQUFRO29CQUNSQyxRQUFRO29CQUNSQyxRQUFRO29CQUNSQyxRQUFRO29CQUNSQyxRQUFRO29CQUNSQyxRQUFRO29CQUNSQyxXQUFXO29CQUNYQyxPQUFPO29CQUNQQyxRQUFRO29CQUNSQyxTQUFTO29CQUNUQyxXQUFXO29CQUNYQyxXQUFXO29CQUNYQyxVQUFVO29CQUNWQyxNQUFNO29CQUNOQyxRQUFRO29CQUNSQyxPQUFPO29CQUNQQyxhQUFhO29CQUNiQyxTQUFTO29CQUNUQyxTQUFTO29CQUNUQyxNQUFNO29CQUNOQyxTQUFTO29CQUNUQyxPQUFPO29CQUNQQyxhQUFhO29CQUNiQyxTQUFTO29CQUNUQyxTQUFTO29CQUNUQyxPQUFPO29CQUNQQyxNQUFNO29CQUNOQyxLQUFLO2dCQUNQO2dCQUNBTixPQUFPN0IsZ0JBQWdCLGFBQWE7b0JBQ2xDO29CQUNBO29CQUNBO29CQUNBO29CQUNBO29CQUNBO29CQUNBO29CQUNBO2lCQUNELEdBQUc7b0JBQ0Y7b0JBQ0E7b0JBQ0E7b0JBQ0E7b0JBQ0E7b0JBQ0E7b0JBQ0E7b0JBQ0E7aUJBQ0Q7Z0JBQ0RvQyxNQUFNO29CQUFDO29CQUFRO29CQUFRO2lCQUFPO2dCQUM5QkMsY0FBYztvQkFBQztpQkFBTztnQkFDdEJ0QixPQUFPO2dCQUNQdUIsV0FBVztvQkFBQztvQkFBRztpQkFBSztnQkFDcEJDLFVBQVU7Z0JBQ1ZDLE1BQU07Z0JBQ05DLFVBQVU7Z0JBQ1ZDLGVBQWU7Z0JBQ2ZDLGFBQWE7Z0JBQ2JDLFFBQVE7WUFDVjtZQUVBLElBQUksQ0FBQzdKLG1CQUFtQm1CLE9BQU8sRUFBRXVGLE9BQU9NLFNBQVM7Z0JBQy9DeEgsa0JBQWtCVTtnQkFDbEJSLGlCQUFpQjtnQkFDakJFLGFBQWE7Z0JBRWIsbUJBQW1CO2dCQUNuQmtGLFdBQVc7b0JBQ1QsSUFBSXpGLFVBQVU4QixPQUFPLEVBQUU7d0JBQ3JCOUIsVUFBVThCLE9BQU8sQ0FBQ00sU0FBUyxDQUFDRSxHQUFHLENBQUM7d0JBQ2hDeUI7d0JBQ0FsQzt3QkFFQSxZQUFZO3dCQUNaLE1BQU1tQyxpQkFBaUI7NEJBQ3JCLE1BQU1DLGNBQWNqRSxVQUFVOEIsT0FBTyxFQUFFRyxpQkFBaUI7NEJBQ3hEZ0MsYUFBYS9CLFFBQVEsQ0FBQ0s7Z0NBQ3BCLE1BQU02QixhQUFhN0I7Z0NBRW5CLElBQUksQ0FBQzZCLFdBQVdDLFFBQVEsRUFBRTtvQ0FDeEJELFdBQVdZLGdCQUFnQixDQUFDLFNBQVM7d0NBQ25DakUsUUFBUUMsSUFBSSxDQUFDLGNBQWNvRCxXQUFXZCxHQUFHO3dDQUN6Q2MsV0FBV1osS0FBSyxDQUFDZ0IsZUFBZSxHQUFHO3dDQUNuQ0osV0FBV1osS0FBSyxDQUFDaUIsTUFBTSxHQUFHO29DQUM1QjtvQ0FFQUwsV0FBV1ksZ0JBQWdCLENBQUMsUUFBUTt3Q0FDbENaLFdBQVdaLEtBQUssQ0FBQ2dCLGVBQWUsR0FBRzt3Q0FDbkNKLFdBQVdaLEtBQUssQ0FBQ2lCLE1BQU0sR0FBRztvQ0FDNUI7Z0NBQ0Y7NEJBQ0Y7d0JBQ0Y7d0JBRUFUO3dCQUVBLGdDQUFnQzt3QkFDaEMsTUFBTXlHLHNCQUFzQnpLLFVBQVU4QixPQUFPLENBQUNHLGdCQUFnQixDQUFDO3dCQUMvRHdJLG9CQUFvQnZJLE9BQU8sQ0FBQyxDQUFDc0U7NEJBQzNCLElBQUksQ0FBQ0EsTUFBTXBFLFNBQVMsQ0FBQ0MsUUFBUSxDQUFDLGtCQUFrQjtnQ0FDOUNtRSxNQUFNN0MsWUFBWSxDQUFDLFNBQVM7NEJBQzlCO3dCQUNGO29CQUNGO2dCQUNGLEdBQUc7Z0JBRUgsdUJBQXVCO2dCQUN2QixNQUFNK0csV0FBVyxJQUFJQyxpQkFBaUIsQ0FBQ0M7b0JBQ3JDL0k7b0JBRUEsa0JBQWtCO29CQUNsQitJLFVBQVUxSSxPQUFPLENBQUMsQ0FBQzJJO3dCQUNqQkEsU0FBU0MsVUFBVSxDQUFDNUksT0FBTyxDQUFDLENBQUM2STs0QkFDM0IsSUFBSUEsS0FBS0MsUUFBUSxLQUFLQyxLQUFLQyxZQUFZLEVBQUU7Z0NBQ3ZDLE1BQU1DLFVBQVVKO2dDQUNoQixxQkFBcUI7Z0NBQ3JCLE1BQU1yRixhQUFheUYsUUFBUS9JLFNBQVMsRUFBRUMsU0FBUyxjQUFjOEksVUFDM0NBLFFBQVEzSSxhQUFhLEdBQUc7Z0NBRTFDLElBQUlrRCxZQUFZO29DQUNkM0UsUUFBUW1ELEdBQUcsQ0FBQztvQ0FDWixzQkFBc0I7b0NBQ3RCd0IsV0FBV3RELFNBQVMsQ0FBQ3dCLE1BQU0sQ0FBQztvQ0FDNUI4QixXQUFXL0IsWUFBWSxDQUFDLFNBQVM7b0NBRWpDLGNBQWM7b0NBQ2Q4QixXQUFXO3dDQUNULE1BQU14QixjQUFjeUIsV0FBV3pELGdCQUFnQixDQUFDO3dDQUNoRGdDLFlBQVkvQixPQUFPLENBQUMsQ0FBQ0ssS0FBYzRCOzRDQUNqQyxNQUFNQyxhQUFhN0I7NENBRW5CLE1BQU1nQyxhQUFhO2dEQUNqQnhELFFBQVFtRCxHQUFHLENBQUMsQ0FBQyxLQUFLLEVBQUVDLE1BQU0sTUFBTSxDQUFDLEVBQUVDLFdBQVdkLEdBQUc7Z0RBQ2pEYyxXQUFXaEMsU0FBUyxDQUFDd0IsTUFBTSxDQUFDLFdBQVc7NENBQ3pDOzRDQUVBLE1BQU1jLGNBQWM7Z0RBQ2xCM0QsUUFBUUMsSUFBSSxDQUFDLENBQUMsS0FBSyxFQUFFbUQsTUFBTSxNQUFNLENBQUMsRUFBRUMsV0FBV2QsR0FBRztnREFDbERjLFdBQVdoQyxTQUFTLENBQUNFLEdBQUcsQ0FBQztnREFFekIsVUFBVTtnREFDVixNQUFNcUMsY0FBY1AsV0FBV2QsR0FBRztnREFDbEMsSUFBSXFCLFlBQVlDLFFBQVEsQ0FBQyxxQkFBcUI7b0RBQzVDLE1BQU1DLFlBQVlGLFlBQVlHLE9BQU8sQ0FBQyx3QkFBd0I7b0RBQzlEVixXQUFXZCxHQUFHLEdBQUd1QjtnREFDbkIsT0FBTyxJQUFJRixZQUFZQyxRQUFRLENBQUMsY0FBYztvREFDNUMsTUFBTUMsWUFBWUYsWUFBWUcsT0FBTyxDQUFDLGFBQWE7b0RBQ25EVixXQUFXZCxHQUFHLEdBQUd1QjtnREFDbkI7NENBQ0Y7NENBRUFULFdBQVdZLGdCQUFnQixDQUFDLFFBQVFULFlBQVk7Z0RBQUVVLE1BQU07NENBQUs7NENBQzdEYixXQUFXWSxnQkFBZ0IsQ0FBQyxTQUFTTixhQUFhO2dEQUFFTyxNQUFNOzRDQUFLO3dDQUNqRTtvQ0FDRixHQUFHO2dDQUNMOzRCQUNGO3dCQUNGO29CQUNGO2dCQUNGO2dCQUVBeUYsU0FBU1UsT0FBTyxDQUFDcEwsVUFBVThCLE9BQU8sRUFBRTtvQkFDbEN1SixXQUFXO29CQUNYQyxTQUFTO2dCQUNYO2dCQUVBLGFBQWE7Z0JBQ2IsTUFBTUMsa0JBQWtCO29CQUN0QmIsU0FBU2MsVUFBVTtnQkFDckI7Z0JBRUEsa0JBQWtCO2dCQUNsQixPQUFPRDtZQUNUO1FBQ0YsRUFBRSxPQUFPL0ssT0FBTztZQUNkLElBQUlBLGlCQUFpQmlMLFNBQVNqTCxNQUFNa0wsSUFBSSxLQUFLLGNBQWM7Z0JBQ3pEM0ssUUFBUVAsS0FBSyxDQUFDLGdDQUFnQ0E7Z0JBQzlDQyxTQUFTO2dCQUNURixhQUFhO1lBQ2Y7UUFDRjtJQUNGLEdBQUc7UUFBQ1Y7UUFBTUk7UUFBZVc7S0FBWTtJQUVyQyx3QkFBd0I7SUFDeEJ0QixnREFBU0EsQ0FBQztRQUNSZSxpQkFBaUI7UUFFakIsa0NBQWtDO1FBQ2xDSyxlQUFlb0IsT0FBTyxHQUFHMkQsV0FBVztZQUNsQytCO1FBQ0YsR0FBRztRQUVILE9BQU87WUFDTCxZQUFZO1lBQ1osSUFBSTlHLGVBQWVvQixPQUFPLEVBQUU7Z0JBQzFCd0YsYUFBYTVHLGVBQWVvQixPQUFPO1lBQ3JDO1lBRUEsT0FBTztZQUNQLElBQUluQixtQkFBbUJtQixPQUFPLEVBQUU7Z0JBQzlCbkIsbUJBQW1CbUIsT0FBTyxDQUFDbUYsS0FBSztZQUNsQztZQUVBLFNBQVM7WUFDVCxJQUFJL0csZ0JBQWdCO2dCQUNsQlUsWUFBWVY7WUFDZDtZQUVBRyxpQkFBaUI7WUFDakJGLGtCQUFrQjtZQUNsQkksYUFBYTtZQUNiRSxTQUFTO1FBQ1g7SUFDRixHQUFHO1FBQUNaO1FBQU1JO1FBQWV1SDtRQUFZNUc7S0FBWTtJQUVqRCxxQkFDRSw4REFBQytLO1FBQUk1TCxXQUFXLENBQUMseUJBQXlCLEVBQUVBLFVBQVUsQ0FBQzs7WUFFcERTLHVCQUNDLDhEQUFDbUw7Z0JBQUk1TCxXQUFVOztrQ0FDYiw4REFBQzZMO2tDQUFHcEw7Ozs7OztrQ0FDSiw4REFBQzhFO3dCQUNDdUcsU0FBUzs0QkFDUHBMLFNBQVM7NEJBQ1QrRzt3QkFDRjt3QkFDQXpILFdBQVU7a0NBQ1g7Ozs7Ozs7Ozs7OzswQkFPTCw4REFBQzRMO2dCQUNDRyxLQUFLOUw7Z0JBQ0xELFdBQVcsQ0FBQyxtREFBbUQsRUFDN0RLLGdCQUFnQixnQkFBZ0IsWUFDakMsQ0FBQztnQkFDRm9ELE9BQU87b0JBQ0wsa0JBQWtCO29CQUNsQix3QkFBd0I7b0JBQ3hCLHlCQUF5QjtvQkFDekIseUJBQXlCO29CQUN6QixxQkFBcUI7b0JBQ3JCLDJCQUEyQjtvQkFDM0IsdUJBQXVCO29CQUN2Qix1QkFBdUI7b0JBQ3ZCLGtCQUFrQjtvQkFDbEIsa0JBQWtCO29CQUNsQiwwQkFBMEI7b0JBQzFCLHdCQUF3QjtvQkFDeEJ1SSxXQUFXM0wsZ0JBQWdCLFNBQVM7Z0JBQ3RDOzs7Ozs7WUFJREUsYUFBYSxDQUFDRSx1QkFDYiw4REFBQ21MO2dCQUFJNUwsV0FBVTs7a0NBQ2IsOERBQUM0TDt3QkFBSTVMLFdBQVU7OzBDQUNiLDhEQUFDNEw7Z0NBQUk1TCxXQUFVOzs7Ozs7MENBQ2YsOERBQUM0TDtnQ0FBSTVMLFdBQVU7Z0NBQXVHeUQsT0FBTztvQ0FBRXdJLG9CQUFvQjtvQ0FBV0MsbUJBQW1CO2dDQUFPOzs7Ozs7Ozs7Ozs7a0NBRTFMLDhEQUFDTjt3QkFBSTVMLFdBQVU7OzBDQUNiLDhEQUFDNkw7Z0NBQUU3TCxXQUFVOzBDQUFzQzs7Ozs7OzBDQUNuRCw4REFBQzZMO2dDQUFFN0wsV0FBVTswQ0FBZ0M7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQU16RDtHQXBtQmdCSDs7UUFFWUYsaURBQVFBOzs7S0FGcEJFO0FBc21CaEIsK0RBQWVBLGFBQWFBLEVBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vc3JjL2NvbXBvbmVudHMvY29tbWVudC9XYWxpbmVDb21tZW50LnRzeD85MTU3Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xuXG5pbXBvcnQgeyBpbml0IH0gZnJvbSAnQHdhbGluZS9jbGllbnQnXG5pbXBvcnQgeyB1c2VFZmZlY3QsIHVzZVJlZiwgdXNlU3RhdGUsIHVzZUNhbGxiYWNrIH0gZnJvbSAncmVhY3QnXG5pbXBvcnQgeyB1c2VUaGVtZSB9IGZyb20gJ25leHQtdGhlbWVzJ1xuaW1wb3J0IHsgZ2V0V2FsaW5lQ29uZmlnIH0gZnJvbSAnQC9saWIvd2FsaW5lQ29uZmlnJ1xuXG5pbnRlcmZhY2UgV2FsaW5lQ29tbWVudFByb3BzIHtcbiAgcGF0aDogc3RyaW5nXG4gIHRpdGxlPzogc3RyaW5nXG4gIGNsYXNzTmFtZT86IHN0cmluZ1xufVxuXG5leHBvcnQgZnVuY3Rpb24gV2FsaW5lQ29tbWVudCh7IHBhdGgsIHRpdGxlLCBjbGFzc05hbWUgPSAnJyB9OiBXYWxpbmVDb21tZW50UHJvcHMpIHtcbiAgY29uc3Qgd2FsaW5lUmVmID0gdXNlUmVmPEhUTUxEaXZFbGVtZW50PihudWxsKVxuICBjb25zdCB7IHJlc29sdmVkVGhlbWUgfSA9IHVzZVRoZW1lKClcbiAgY29uc3QgW3dhbGluZUluc3RhbmNlLCBzZXRXYWxpbmVJbnN0YW5jZV0gPSB1c2VTdGF0ZTxhbnk+KG51bGwpXG4gIGNvbnN0IFtpc0luaXRpYWxpemVkLCBzZXRJc0luaXRpYWxpemVkXSA9IHVzZVN0YXRlKGZhbHNlKVxuICBjb25zdCBbaXNMb2FkaW5nLCBzZXRJc0xvYWRpbmddID0gdXNlU3RhdGUodHJ1ZSlcbiAgY29uc3QgW2Vycm9yLCBzZXRFcnJvcl0gPSB1c2VTdGF0ZTxzdHJpbmcgfCBudWxsPihudWxsKVxuICBjb25zdCBpbml0VGltZW91dFJlZiA9IHVzZVJlZjxOb2RlSlMuVGltZW91dCB8IG51bGw+KG51bGwpXG4gIGNvbnN0IGFib3J0Q29udHJvbGxlclJlZiA9IHVzZVJlZjxBYm9ydENvbnRyb2xsZXIgfCBudWxsPihudWxsKVxuXG4gIC8vIOWuieWFqOmUgOavgeWunuS+i+eahOWHveaVsFxuICBjb25zdCBzYWZlRGVzdHJveSA9IHVzZUNhbGxiYWNrKChpbnN0YW5jZTogYW55KSA9PiB7XG4gICAgaWYgKGluc3RhbmNlICYmIHR5cGVvZiBpbnN0YW5jZS5kZXN0cm95ID09PSAnZnVuY3Rpb24nKSB7XG4gICAgICB0cnkge1xuICAgICAgICBpbnN0YW5jZS5kZXN0cm95KClcbiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgIGNvbnNvbGUud2FybignV2FsaW5lIGRlc3Ryb3kgZXJyb3I6JywgZXJyb3IpXG4gICAgICB9XG4gICAgfVxuICB9LCBbXSlcblxuICAvLyDnlJ/miJDpmo/mnLrlpLTlg49VUkxcbiAgY29uc3QgZ2VuZXJhdGVSYW5kb21BdmF0YXIgPSB1c2VDYWxsYmFjaygoc2VlZDogc3RyaW5nKTogc3RyaW5nID0+IHtcbiAgICBjb25zdCBhdmF0YXJTdHlsZXMgPSBbXG4gICAgICAnYWR2ZW50dXJlcicsXG4gICAgICAnYWR2ZW50dXJlci1uZXV0cmFsJyxcbiAgICAgICdhdmF0YWFhcnMnLFxuICAgICAgJ2F2YXRhYWFycy1uZXV0cmFsJyxcbiAgICAgICdib3R0dHMnLFxuICAgICAgJ2JvdHR0cy1uZXV0cmFsJyxcbiAgICAgICdmdW4tZW1vamknLFxuICAgICAgJ2ljb25zJyxcbiAgICAgICdpZGVudGljb24nLFxuICAgICAgJ2luaXRpYWxzJyxcbiAgICAgICdsb3JlbGVpJyxcbiAgICAgICdtaWNhaCcsXG4gICAgICAnbWluaWF2cycsXG4gICAgICAnb3Blbi1wZWVwcycsXG4gICAgICAncGVyc29uYXMnLFxuICAgICAgJ3BpeGVsLWFydCcsXG4gICAgICAncGl4ZWwtYXJ0LW5ldXRyYWwnXG4gICAgXVxuXG4gICAgY29uc3Qgc3R5bGVJbmRleCA9IHNlZWQuc3BsaXQoJycpLnJlZHVjZSgoYWNjLCBjaGFyKSA9PiBhY2MgKyBjaGFyLmNoYXJDb2RlQXQoMCksIDApICUgYXZhdGFyU3R5bGVzLmxlbmd0aFxuICAgIGNvbnN0IHNlbGVjdGVkU3R5bGUgPSBhdmF0YXJTdHlsZXNbc3R5bGVJbmRleF1cblxuICAgIHJldHVybiBgaHR0cHM6Ly9hcGkuZGljZWJlYXIuY29tLzkueC8ke3NlbGVjdGVkU3R5bGV9L3N2Zz9zZWVkPSR7ZW5jb2RlVVJJQ29tcG9uZW50KHNlZWQpfSZiYWNrZ3JvdW5kQ29sb3I9dHJhbnNwYXJlbnQmcmFkaXVzPTUwYFxuICB9LCBbXSlcblxuICAvLyDlop7lvLrlpLTlg4/mmL7npLpcbiAgY29uc3QgZW5oYW5jZUF2YXRhcnMgPSB1c2VDYWxsYmFjaygoKSA9PiB7XG4gICAgaWYgKCF3YWxpbmVSZWYuY3VycmVudCkgcmV0dXJuXG5cbiAgICBjb25zdCBjb250YWluZXIgPSB3YWxpbmVSZWYuY3VycmVudFxuICAgIGNvbnN0IGF2YXRhcnMgPSBjb250YWluZXIucXVlcnlTZWxlY3RvckFsbCgnLndsLWF2YXRhcicpXG5cbiAgICBhdmF0YXJzLmZvckVhY2goYXZhdGFyID0+IHtcbiAgICAgIGlmICghYXZhdGFyLmNsYXNzTGlzdC5jb250YWlucygnd2wtYXZhdGFyLWVuaGFuY2VkJykpIHtcbiAgICAgICAgYXZhdGFyLmNsYXNzTGlzdC5hZGQoJ3dsLWF2YXRhci1lbmhhbmNlZCcpXG5cbiAgICAgICAgY29uc3QgaW1nID0gYXZhdGFyLnF1ZXJ5U2VsZWN0b3IoJ2ltZycpXG4gICAgICAgIGlmICghaW1nKSB7XG4gICAgICAgICAgY29uc3QgY29tbWVudENhcmQgPSBhdmF0YXIuY2xvc2VzdCgnLndsLWNhcmQnKVxuICAgICAgICAgIGNvbnN0IHVzZXJOaWNrID0gY29tbWVudENhcmQ/LnF1ZXJ5U2VsZWN0b3IoJy53bC1uaWNrJyk/LnRleHRDb250ZW50IHx8ICdhbm9ueW1vdXMnXG4gICAgICAgICAgY29uc3QgdXNlck1haWwgPSBjb21tZW50Q2FyZD8ucXVlcnlTZWxlY3RvcignLndsLW1haWwnKT8udGV4dENvbnRlbnQgfHwgJydcblxuICAgICAgICAgIGNvbnN0IHNlZWQgPSB1c2VyTWFpbCB8fCB1c2VyTmljayB8fCBgdXNlci0ke01hdGgucmFuZG9tKCkudG9TdHJpbmcoMzYpLnN1YnN0cigyLCA5KX1gXG4gICAgICAgICAgY29uc3QgYXZhdGFyVXJsID0gZ2VuZXJhdGVSYW5kb21BdmF0YXIoc2VlZClcblxuICAgICAgICAgIGNvbnN0IGF2YXRhckltZyA9IGRvY3VtZW50LmNyZWF0ZUVsZW1lbnQoJ2ltZycpXG4gICAgICAgICAgYXZhdGFySW1nLnNyYyA9IGF2YXRhclVybFxuICAgICAgICAgIGF2YXRhckltZy5hbHQgPSBgJHt1c2VyTmlja30ncyBhdmF0YXJgXG4gICAgICAgICAgYXZhdGFySW1nLnN0eWxlLmNzc1RleHQgPSBgXG4gICAgICAgICAgICB3aWR0aDogY2FsYygxMDAlIC0gNHB4KSAhaW1wb3J0YW50O1xuICAgICAgICAgICAgaGVpZ2h0OiBjYWxjKDEwMCUgLSA0cHgpICFpbXBvcnRhbnQ7XG4gICAgICAgICAgICBib3JkZXItcmFkaXVzOiA1MCUgIWltcG9ydGFudDtcbiAgICAgICAgICAgIG9iamVjdC1maXQ6IGNvdmVyICFpbXBvcnRhbnQ7XG4gICAgICAgICAgICBwb3NpdGlvbjogYWJzb2x1dGUgIWltcG9ydGFudDtcbiAgICAgICAgICAgIHRvcDogMnB4ICFpbXBvcnRhbnQ7XG4gICAgICAgICAgICBsZWZ0OiAycHggIWltcG9ydGFudDtcbiAgICAgICAgICAgIHotaW5kZXg6IDEgIWltcG9ydGFudDtcbiAgICAgICAgICAgIHRyYW5zaXRpb246IGFsbCAwLjNzIGVhc2UgIWltcG9ydGFudDtcbiAgICAgICAgICBgXG5cbiAgICAgICAgICBhdmF0YXJJbWcub25lcnJvciA9ICgpID0+IHtcbiAgICAgICAgICAgIGF2YXRhci5zZXRBdHRyaWJ1dGUoJ2RhdGEtbm8tYXZhdGFyJywgJ3RydWUnKVxuICAgICAgICAgICAgYXZhdGFySW1nLnJlbW92ZSgpXG4gICAgICAgICAgfVxuXG4gICAgICAgICAgYXZhdGFyLmFwcGVuZENoaWxkKGF2YXRhckltZylcbiAgICAgICAgICBhdmF0YXIucmVtb3ZlQXR0cmlidXRlKCdkYXRhLW5vLWF2YXRhcicpXG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9KVxuICB9LCBbZ2VuZXJhdGVSYW5kb21BdmF0YXJdKVxuXG4gIC8vIOS/ruWkjeihqOaDheW8ueeql+WumuS9jeWSjOWxgue6p+mXrumimFxuICBjb25zdCBmaXhFbW9qaVBvcHVwcyA9IHVzZUNhbGxiYWNrKCgpID0+IHtcbiAgICBpZiAoIXdhbGluZVJlZi5jdXJyZW50KSByZXR1cm5cblxuICAgIGNvbnN0IGNvbnRhaW5lciA9IHdhbGluZVJlZi5jdXJyZW50XG5cbiAgICAvLyDkv67lpI3ooajmg4XljIXlm77niYfliqDovb0gLSDlop7lvLrniYhcbiAgICBjb25zdCBmaXhFbW9qaUltYWdlcyA9ICgpID0+IHtcbiAgICAgIGNvbnN0IGVtb2ppSW1hZ2VzID0gY29udGFpbmVyLnF1ZXJ5U2VsZWN0b3JBbGwoJy53bC1lbW9qaSBpbWcnKVxuICAgICAgY29uc29sZS5sb2coYOaJvuWIsCAke2Vtb2ppSW1hZ2VzLmxlbmd0aH0g5Liq6KGo5oOF5YyF5Zu+54mHYClcblxuICAgICAgZW1vamlJbWFnZXMuZm9yRWFjaCgoaW1nOiBFbGVtZW50LCBpbmRleDogbnVtYmVyKSA9PiB7XG4gICAgICAgIGNvbnN0IGltZ0VsZW1lbnQgPSBpbWcgYXMgSFRNTEltYWdlRWxlbWVudFxuXG4gICAgICAgIC8vIOa3u+WKoOWKoOi9veeKtuaAgVxuICAgICAgICBpbWdFbGVtZW50LmNsYXNzTGlzdC5hZGQoJ2xvYWRpbmcnKVxuXG4gICAgICAgIC8vIOajgOafpeWbvueJh+aYr+WQpuW3sue7j+WKoOi9veWujOaIkFxuICAgICAgICBpZiAoaW1nRWxlbWVudC5jb21wbGV0ZSAmJiBpbWdFbGVtZW50Lm5hdHVyYWxXaWR0aCA+IDApIHtcbiAgICAgICAgICBjb25zb2xlLmxvZyhg6KGo5oOF5YyFICR7aW5kZXh9IOW3suWKoOi9veWujOaIkDpgLCBpbWdFbGVtZW50LnNyYylcbiAgICAgICAgICBpbWdFbGVtZW50LmNsYXNzTGlzdC5yZW1vdmUoJ2xvYWRpbmcnKVxuICAgICAgICAgIHJldHVyblxuICAgICAgICB9XG5cbiAgICAgICAgLy8g5re75Yqg5Yqg6L295oiQ5Yqf5aSE55CGXG4gICAgICAgIGNvbnN0IGhhbmRsZUxvYWQgPSAoKSA9PiB7XG4gICAgICAgICAgY29uc29sZS5sb2coYOihqOaDheWMhSAke2luZGV4fSDliqDovb3miJDlip86YCwgaW1nRWxlbWVudC5zcmMpXG4gICAgICAgICAgaW1nRWxlbWVudC5jbGFzc0xpc3QucmVtb3ZlKCdsb2FkaW5nJywgJ2Vycm9yJylcbiAgICAgICAgICBpbWdFbGVtZW50LnN0eWxlLmJhY2tncm91bmRDb2xvciA9ICd0cmFuc3BhcmVudCdcbiAgICAgICAgICBpbWdFbGVtZW50LnN0eWxlLmJvcmRlciA9ICdub25lJ1xuICAgICAgICB9XG5cbiAgICAgICAgLy8g5re75Yqg5Yqg6L295aSx6LSl5aSE55CGXG4gICAgICAgIGNvbnN0IGhhbmRsZUVycm9yID0gKCkgPT4ge1xuICAgICAgICAgIGNvbnNvbGUud2Fybihg6KGo5oOF5YyFICR7aW5kZXh9IOWKoOi9veWksei0pTpgLCBpbWdFbGVtZW50LnNyYylcbiAgICAgICAgICBpbWdFbGVtZW50LmNsYXNzTGlzdC5yZW1vdmUoJ2xvYWRpbmcnKVxuICAgICAgICAgIGltZ0VsZW1lbnQuY2xhc3NMaXN0LmFkZCgnZXJyb3InKVxuXG4gICAgICAgICAgLy8g5bCd6K+V5L2/55So5aSH55SoQ0ROXG4gICAgICAgICAgY29uc3Qgb3JpZ2luYWxTcmMgPSBpbWdFbGVtZW50LnNyY1xuICAgICAgICAgIGlmIChvcmlnaW5hbFNyYy5pbmNsdWRlcygnY2RuLmpzZGVsaXZyLm5ldCcpKSB7XG4gICAgICAgICAgICBjb25zdCBiYWNrdXBTcmMgPSBvcmlnaW5hbFNyYy5yZXBsYWNlKCdjZG4uanNkZWxpdnIubmV0L25wbScsICd1bnBrZy5jb20nKVxuICAgICAgICAgICAgY29uc29sZS5sb2coYOWwneivleWkh+eUqENETjpgLCBiYWNrdXBTcmMpXG4gICAgICAgICAgICBpbWdFbGVtZW50LnNyYyA9IGJhY2t1cFNyY1xuICAgICAgICAgIH0gZWxzZSBpZiAob3JpZ2luYWxTcmMuaW5jbHVkZXMoJ3VucGtnLmNvbScpKSB7XG4gICAgICAgICAgICBjb25zdCBiYWNrdXBTcmMgPSBvcmlnaW5hbFNyYy5yZXBsYWNlKCd1bnBrZy5jb20nLCAnY2RuLmpzZGVsaXZyLm5ldC9ucG0nKVxuICAgICAgICAgICAgY29uc29sZS5sb2coYOWwneivleWkh+eUqENETjpgLCBiYWNrdXBTcmMpXG4gICAgICAgICAgICBpbWdFbGVtZW50LnNyYyA9IGJhY2t1cFNyY1xuICAgICAgICAgIH1cbiAgICAgICAgfVxuXG4gICAgICAgIC8vIOenu+mZpOaXp+eahOS6i+S7tuebkeWQrOWZqO+8iOWmguaenOWtmOWcqO+8iVxuICAgICAgICBpbWdFbGVtZW50LnJlbW92ZUV2ZW50TGlzdGVuZXIoJ2xvYWQnLCBoYW5kbGVMb2FkKVxuICAgICAgICBpbWdFbGVtZW50LnJlbW92ZUV2ZW50TGlzdGVuZXIoJ2Vycm9yJywgaGFuZGxlRXJyb3IpXG5cbiAgICAgICAgLy8g5re75Yqg5paw55qE5LqL5Lu255uR5ZCs5ZmoXG4gICAgICAgIGltZ0VsZW1lbnQuYWRkRXZlbnRMaXN0ZW5lcignbG9hZCcsIGhhbmRsZUxvYWQsIHsgb25jZTogdHJ1ZSB9KVxuICAgICAgICBpbWdFbGVtZW50LmFkZEV2ZW50TGlzdGVuZXIoJ2Vycm9yJywgaGFuZGxlRXJyb3IsIHsgb25jZTogdHJ1ZSB9KVxuXG4gICAgICAgIC8vIOWmguaenOWbvueJh3NyY+S4uuepuuaIluaXoOaViO+8jOWwneivlemHjeaWsOiuvue9rlxuICAgICAgICBpZiAoIWltZ0VsZW1lbnQuc3JjIHx8IGltZ0VsZW1lbnQuc3JjID09PSB3aW5kb3cubG9jYXRpb24uaHJlZikge1xuICAgICAgICAgIGNvbnNvbGUud2Fybihg6KGo5oOF5YyFICR7aW5kZXh9IHNyY+aXoOaViO+8jOWwneivleS/ruWkjWApXG4gICAgICAgICAgLy8g6L+Z6YeM5Y+v5Lul5re75Yqg6YeN5paw6I635Y+Wc3Jj55qE6YC76L6RXG4gICAgICAgIH1cbiAgICAgIH0pXG4gICAgfVxuXG4gICAgLy8g5L+u5aSN6KGo5oOF5by556qXIC0g5L2/55So5paw55qE6YCJ5oup5ZmoXG4gICAgY29uc3QgZW1vamlCdXR0b25zID0gY29udGFpbmVyLnF1ZXJ5U2VsZWN0b3JBbGwoJ1tkYXRhLXdhbGluZV0gLndsLWFjdGlvbicpXG4gICAgZW1vamlCdXR0b25zLmZvckVhY2goKGJ1dHRvbikgPT4ge1xuICAgICAgaWYgKGJ1dHRvbi50ZXh0Q29udGVudD8uaW5jbHVkZXMoJ/CfmIAnKSB8fCBidXR0b24uZ2V0QXR0cmlidXRlKCd0aXRsZScpPy5pbmNsdWRlcygnZW1vamknKSkge1xuICAgICAgICBidXR0b24uYWRkRXZlbnRMaXN0ZW5lcignY2xpY2snLCAoZSkgPT4ge1xuICAgICAgICAgIHNldFRpbWVvdXQoKCkgPT4ge1xuICAgICAgICAgICAgLy8g5p+l5om+6KGo5oOF5YyF5by556qXIC0g5pSv5oyB5aSa56eN5Y+v6IO955qE57G75ZCNXG4gICAgICAgICAgICBjb25zdCBlbW9qaVBvcHVwID0gY29udGFpbmVyLnF1ZXJ5U2VsZWN0b3IoJ1tkYXRhLXdhbGluZV0gLndsLWVtb2ppJykgfHxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnRhaW5lci5xdWVyeVNlbGVjdG9yKCdbZGF0YS13YWxpbmVdIC53bC1lbW9qaS1wb3B1cCcpXG5cbiAgICAgICAgICAgIGlmIChlbW9qaVBvcHVwKSB7XG4gICAgICAgICAgICAgIC8vIOehruS/neW8ueeql+ato+ehruaYvuekulxuICAgICAgICAgICAgICBlbW9qaVBvcHVwLmNsYXNzTGlzdC5hZGQoJ3dsLWVtb2ppLW9wZW4nKVxuICAgICAgICAgICAgICBlbW9qaVBvcHVwLnN0eWxlLmRpc3BsYXkgPSAnZmxleCdcbiAgICAgICAgICAgICAgZW1vamlQb3B1cC5zdHlsZS5vcGFjaXR5ID0gJzEnXG4gICAgICAgICAgICAgIGVtb2ppUG9wdXAuc3R5bGUudmlzaWJpbGl0eSA9ICd2aXNpYmxlJ1xuXG4gICAgICAgICAgICAgIC8vIOS/ruWkjeihqOaDheWMheWbvueJh1xuICAgICAgICAgICAgICBmaXhFbW9qaUltYWdlcygpXG5cbiAgICAgICAgICAgICAgLy8g5re75Yqg5aS06YOo5ZKM5YWz6Zet5oyJ6ZKu77yI5aaC5p6c5LiN5a2Y5Zyo77yJXG4gICAgICAgICAgICAgIGlmICghZW1vamlQb3B1cC5xdWVyeVNlbGVjdG9yKCcud2wtcG9wdXAtaGVhZGVyJykpIHtcbiAgICAgICAgICAgICAgICBjb25zdCBoZWFkZXIgPSBkb2N1bWVudC5jcmVhdGVFbGVtZW50KCdkaXYnKVxuICAgICAgICAgICAgICAgIGhlYWRlci5jbGFzc05hbWUgPSAnd2wtcG9wdXAtaGVhZGVyJ1xuICAgICAgICAgICAgICAgIGhlYWRlci5pbm5lckhUTUwgPSBgXG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzPVwid2wtcG9wdXAtdGl0bGVcIj5DaG9vc2UgRW1vamk8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDxidXR0b24gY2xhc3M9XCJ3bC1wb3B1cC1jbG9zZVwiIHR5cGU9XCJidXR0b25cIj7DlzwvYnV0dG9uPlxuICAgICAgICAgICAgICAgIGBcbiAgICAgICAgICAgICAgICBlbW9qaVBvcHVwLmluc2VydEJlZm9yZShoZWFkZXIsIGVtb2ppUG9wdXAuZmlyc3RDaGlsZClcblxuICAgICAgICAgICAgICAgIC8vIOa3u+WKoOWFs+mXreaMiemSruS6i+S7tlxuICAgICAgICAgICAgICAgIGNvbnN0IGNsb3NlQnRuID0gaGVhZGVyLnF1ZXJ5U2VsZWN0b3IoJy53bC1wb3B1cC1jbG9zZScpXG4gICAgICAgICAgICAgICAgaWYgKGNsb3NlQnRuKSB7XG4gICAgICAgICAgICAgICAgICBjbG9zZUJ0bi5hZGRFdmVudExpc3RlbmVyKCdjbGljaycsICgpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgY2xvc2VFbW9qaVBvcHVwKGVtb2ppUG9wdXApXG4gICAgICAgICAgICAgICAgICB9KVxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgfVxuXG4gICAgICAgICAgICAgIC8vIOa3u+WKoOiDjOaZr+eCueWHu+WFs+mXrVxuICAgICAgICAgICAgICBjb25zdCBoYW5kbGVCYWNrZ3JvdW5kQ2xpY2sgPSAoZTogRXZlbnQpID0+IHtcbiAgICAgICAgICAgICAgICBpZiAoZS50YXJnZXQgPT09IGVtb2ppUG9wdXApIHtcbiAgICAgICAgICAgICAgICAgIGNsb3NlRW1vamlQb3B1cChlbW9qaVBvcHVwKVxuICAgICAgICAgICAgICAgICAgZG9jdW1lbnQucmVtb3ZlRXZlbnRMaXN0ZW5lcignY2xpY2snLCBoYW5kbGVCYWNrZ3JvdW5kQ2xpY2spXG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICB9XG5cbiAgICAgICAgICAgICAgc2V0VGltZW91dCgoKSA9PiB7XG4gICAgICAgICAgICAgICAgZG9jdW1lbnQuYWRkRXZlbnRMaXN0ZW5lcignY2xpY2snLCBoYW5kbGVCYWNrZ3JvdW5kQ2xpY2spXG4gICAgICAgICAgICAgIH0sIDEwMClcblxuICAgICAgICAgICAgICAvLyDmt7vliqBFU0PplK7lhbPpl61cbiAgICAgICAgICAgICAgY29uc3QgaGFuZGxlRXNjS2V5ID0gKGU6IEtleWJvYXJkRXZlbnQpID0+IHtcbiAgICAgICAgICAgICAgICBpZiAoZS5rZXkgPT09ICdFc2NhcGUnKSB7XG4gICAgICAgICAgICAgICAgICBjbG9zZUVtb2ppUG9wdXAoZW1vamlQb3B1cClcbiAgICAgICAgICAgICAgICAgIGRvY3VtZW50LnJlbW92ZUV2ZW50TGlzdGVuZXIoJ2tleWRvd24nLCBoYW5kbGVFc2NLZXkpXG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgIGRvY3VtZW50LmFkZEV2ZW50TGlzdGVuZXIoJ2tleWRvd24nLCBoYW5kbGVFc2NLZXkpXG4gICAgICAgICAgICB9XG4gICAgICAgICAgfSwgNTApXG4gICAgICAgIH0pXG4gICAgICB9XG4gICAgfSlcblxuICAgIC8vIOWFs+mXreihqOaDheWMheW8ueeql+eahOWHveaVsFxuICAgIGNvbnN0IGNsb3NlRW1vamlQb3B1cCA9IChwb3B1cDogRWxlbWVudCkgPT4ge1xuICAgICAgcG9wdXAuY2xhc3NMaXN0LnJlbW92ZSgnd2wtZW1vamktb3BlbicsICdkaXNwbGF5JylcbiAgICAgIHBvcHVwLnNldEF0dHJpYnV0ZSgnc3R5bGUnLCAnZGlzcGxheTogbm9uZSAhaW1wb3J0YW50OyBvcGFjaXR5OiAwICFpbXBvcnRhbnQ7IHZpc2liaWxpdHk6IGhpZGRlbiAhaW1wb3J0YW50OycpXG4gICAgfVxuXG4gICAgLy8g5L+u5aSNR0lG5by556qX77yI5aaC5p6c5a2Y5Zyo77yJXG4gICAgY29uc3QgZ2lmQnV0dG9ucyA9IGNvbnRhaW5lci5xdWVyeVNlbGVjdG9yQWxsKCdbZGF0YS13YWxpbmVdIC53bC1hY3Rpb24nKVxuICAgIGdpZkJ1dHRvbnMuZm9yRWFjaCgoYnV0dG9uKSA9PiB7XG4gICAgICBpZiAoYnV0dG9uLnRleHRDb250ZW50Py5pbmNsdWRlcygnR0lGJykgfHwgYnV0dG9uLmdldEF0dHJpYnV0ZSgndGl0bGUnKT8uaW5jbHVkZXMoJ2dpZicpKSB7XG4gICAgICAgIGJ1dHRvbi5hZGRFdmVudExpc3RlbmVyKCdjbGljaycsIChlKSA9PiB7XG4gICAgICAgICAgc2V0VGltZW91dCgoKSA9PiB7XG4gICAgICAgICAgICBjb25zdCBnaWZQb3B1cCA9IGNvbnRhaW5lci5xdWVyeVNlbGVjdG9yKCdbZGF0YS13YWxpbmVdIC53bC1naWYtcG9wdXAnKVxuICAgICAgICAgICAgaWYgKGdpZlBvcHVwKSB7XG4gICAgICAgICAgICAgIC8vIOa3u+WKoOWktOmDqOWSjOWFs+mXreaMiemSru+8iOWmguaenOS4jeWtmOWcqO+8iVxuICAgICAgICAgICAgICBpZiAoIWdpZlBvcHVwLnF1ZXJ5U2VsZWN0b3IoJy53bC1wb3B1cC1oZWFkZXInKSkge1xuICAgICAgICAgICAgICAgIGNvbnN0IGhlYWRlciA9IGRvY3VtZW50LmNyZWF0ZUVsZW1lbnQoJ2RpdicpXG4gICAgICAgICAgICAgICAgaGVhZGVyLmNsYXNzTmFtZSA9ICd3bC1wb3B1cC1oZWFkZXInXG4gICAgICAgICAgICAgICAgaGVhZGVyLmlubmVySFRNTCA9IGBcbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3M9XCJ3bC1wb3B1cC10aXRsZVwiPumAieaLqUdJRjwvZGl2PlxuICAgICAgICAgICAgICAgICAgPGJ1dHRvbiBjbGFzcz1cIndsLXBvcHVwLWNsb3NlXCIgdHlwZT1cImJ1dHRvblwiPsOXPC9idXR0b24+XG4gICAgICAgICAgICAgICAgYFxuICAgICAgICAgICAgICAgIGdpZlBvcHVwLmluc2VydEJlZm9yZShoZWFkZXIsIGdpZlBvcHVwLmZpcnN0Q2hpbGQpXG5cbiAgICAgICAgICAgICAgICAvLyDmt7vliqDlhbPpl63mjInpkq7kuovku7ZcbiAgICAgICAgICAgICAgICBjb25zdCBjbG9zZUJ0biA9IGhlYWRlci5xdWVyeVNlbGVjdG9yKCcud2wtcG9wdXAtY2xvc2UnKVxuICAgICAgICAgICAgICAgIGlmIChjbG9zZUJ0bikge1xuICAgICAgICAgICAgICAgICAgY2xvc2VCdG4uYWRkRXZlbnRMaXN0ZW5lcignY2xpY2snLCAoKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgIGdpZlBvcHVwLmNsYXNzTGlzdC5yZW1vdmUoJ2Rpc3BsYXknKVxuICAgICAgICAgICAgICAgICAgfSlcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9LCA1MClcbiAgICAgICAgfSlcbiAgICAgIH1cbiAgICB9KVxuICB9LCBbXSlcblxuICAvLyDmtYvor5Xooajmg4XljIVDRE7lj6/orr/pl67mgKdcbiAgY29uc3QgdGVzdEVtb2ppQ0ROID0gYXN5bmMgKCkgPT4ge1xuICAgIGNvbnN0IHRlc3RVcmxzID0gW1xuICAgICAgJ2h0dHBzOi8vY2RuLmpzZGVsaXZyLm5ldC9ucG0vQHdhbGluZS9lbW9qaXNAMS4yLjAvd2VpYm8vaW5mby5qc29uJyxcbiAgICAgICdodHRwczovL3VucGtnLmNvbS9Ad2FsaW5lL2Vtb2ppc0AxLjIuMC93ZWliby9pbmZvLmpzb24nXG4gICAgXVxuXG4gICAgZm9yIChjb25zdCB1cmwgb2YgdGVzdFVybHMpIHtcbiAgICAgIHRyeSB7XG4gICAgICAgIGNvbnN0IGNvbnRyb2xsZXIgPSBuZXcgQWJvcnRDb250cm9sbGVyKClcbiAgICAgICAgY29uc3QgdGltZW91dElkID0gc2V0VGltZW91dCgoKSA9PiBjb250cm9sbGVyLmFib3J0KCksIDMwMDApIC8vIDPnp5LotoXml7ZcblxuICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKHVybCwge1xuICAgICAgICAgIG1ldGhvZDogJ0hFQUQnLFxuICAgICAgICAgIHNpZ25hbDogY29udHJvbGxlci5zaWduYWxcbiAgICAgICAgfSlcblxuICAgICAgICBjbGVhclRpbWVvdXQodGltZW91dElkKVxuXG4gICAgICAgIGlmIChyZXNwb25zZS5vaykge1xuICAgICAgICAgIGNvbnNvbGUubG9nKGDooajmg4XljIVDRE7lj6/orr/pl646ICR7dXJsfWApXG4gICAgICAgICAgcmV0dXJuIHVybC5pbmNsdWRlcygnY2RuLmpzZGVsaXZyLm5ldCcpID8gJ2pzZGVsaXZyJyA6ICd1bnBrZydcbiAgICAgICAgfVxuICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgY29uc29sZS53YXJuKGDooajmg4XljIVDRE7kuI3lj6/orr/pl646ICR7dXJsfWAsIGVycm9yKVxuICAgICAgfVxuICAgIH1cblxuICAgIGNvbnNvbGUud2Fybign5omA5pyJ6KGo5oOF5YyFQ0RO6YO95LiN5Y+v6K6/6Zeu77yM5L2/55So5pys5Zyw6KGo5oOF5YyFJylcbiAgICByZXR1cm4gJ2xvY2FsJyAvLyDkvb/nlKjmnKzlnLDooajmg4XljIVcbiAgfVxuXG4gIC8vIOWIneWni+WMlldhbGluZeeahOWHveaVsFxuICBjb25zdCBpbml0V2FsaW5lID0gdXNlQ2FsbGJhY2soYXN5bmMgKCkgPT4ge1xuICAgIGlmICghd2FsaW5lUmVmLmN1cnJlbnQpIHJldHVyblxuXG4gICAgLy8g5Y+W5raI5LmL5YmN55qE6K+35rGCXG4gICAgaWYgKGFib3J0Q29udHJvbGxlclJlZi5jdXJyZW50KSB7XG4gICAgICBhYm9ydENvbnRyb2xsZXJSZWYuY3VycmVudC5hYm9ydCgpXG4gICAgfVxuXG4gICAgLy8g5Yib5bu65paw55qEQWJvcnRDb250cm9sbGVyXG4gICAgYWJvcnRDb250cm9sbGVyUmVmLmN1cnJlbnQgPSBuZXcgQWJvcnRDb250cm9sbGVyKClcblxuICAgIHRyeSB7XG4gICAgICBzZXRJc0xvYWRpbmcodHJ1ZSlcbiAgICAgIHNldEVycm9yKG51bGwpXG5cbiAgICAgIC8vIOW7tui/n+WIneWni+WMlu+8jOmBv+WFjeW/q+mAn+WIh+aNouWvvOiHtOeahOmXrumimFxuICAgICAgYXdhaXQgbmV3IFByb21pc2UocmVzb2x2ZSA9PiBzZXRUaW1lb3V0KHJlc29sdmUsIDEwMCkpXG5cbiAgICAgIC8vIOajgOafpee7hOS7tuaYr+WQpuS7jeeEtuWtmOWcqFxuICAgICAgaWYgKCF3YWxpbmVSZWYuY3VycmVudCB8fCBhYm9ydENvbnRyb2xsZXJSZWYuY3VycmVudD8uc2lnbmFsLmFib3J0ZWQpIHtcbiAgICAgICAgcmV0dXJuXG4gICAgICB9XG5cbiAgICAgIC8vIOa1i+ivleihqOaDheWMhUNETlxuICAgICAgY29uc3QgY2RuUHJvdmlkZXIgPSBhd2FpdCB0ZXN0RW1vamlDRE4oKVxuICAgICAgY29uc29sZS5sb2coYOS9v+eUqOihqOaDheWMhUNETjogJHtjZG5Qcm92aWRlcn1gKVxuXG4gICAgICAvLyDojrflj5ZXYWxpbmXphY3nva5cbiAgICAgIGNvbnN0IGNvbmZpZyA9IGF3YWl0IGdldFdhbGluZUNvbmZpZygpXG5cbiAgICAgIC8vIOa4heeQhuWuueWZqFxuICAgICAgd2FsaW5lUmVmLmN1cnJlbnQuaW5uZXJIVE1MID0gJydcblxuICAgICAgLy8g5Yid5aeL5YyWV2FsaW5lXG4gICAgICBjb25zdCBpbnN0YW5jZSA9IGluaXQoe1xuICAgICAgICBlbDogd2FsaW5lUmVmLmN1cnJlbnQsXG4gICAgICAgIHNlcnZlclVSTDogY29uZmlnLnNlcnZlclVSTCxcbiAgICAgICAgcGF0aCxcbiAgICAgICAgZGFyazogcmVzb2x2ZWRUaGVtZSA9PT0gJ2RhcmsnLFxuICAgICAgICBsb2NhbGU6IHtcbiAgICAgICAgICBwbGFjZWhvbGRlcjogJ1NoYXJlIHlvdXIgdGhvdWdodHMgYW5kIGpvaW4gdGhlIGRpc2N1c3Npb24uLi4nLFxuICAgICAgICAgIGFkbWluOiAnQWRtaW4nLFxuICAgICAgICAgIGxldmVsMDogJ05ld2NvbWVyJyxcbiAgICAgICAgICBsZXZlbDE6ICdFeHBsb3JlcicsXG4gICAgICAgICAgbGV2ZWwyOiAnQ29udHJpYnV0b3InLCBcbiAgICAgICAgICBsZXZlbDM6ICdFeHBlcnQnLFxuICAgICAgICAgIGxldmVsNDogJ01hc3RlcicsXG4gICAgICAgICAgbGV2ZWw1OiAnTGVnZW5kJyxcbiAgICAgICAgICBhbm9ueW1vdXM6ICdBbm9ueW1vdXMnLFxuICAgICAgICAgIGxvZ2luOiAnU2lnbiBJbicsXG4gICAgICAgICAgbG9nb3V0OiAnU2lnbiBPdXQnLFxuICAgICAgICAgIHByb2ZpbGU6ICdQcm9maWxlJyxcbiAgICAgICAgICBuaWNrRXJyb3I6ICdOaWNrbmFtZSBtdXN0IGJlIGF0IGxlYXN0IDMgY2hhcmFjdGVycycsXG4gICAgICAgICAgbWFpbEVycm9yOiAnUGxlYXNlIGVudGVyIGEgdmFsaWQgZW1haWwgYWRkcmVzcycsXG4gICAgICAgICAgd29yZEhpbnQ6ICdQbGVhc2UgZW50ZXIgeW91ciBjb21tZW50JyxcbiAgICAgICAgICBzb2ZhOiAnQmUgdGhlIGZpcnN0IHRvIHNoYXJlIHlvdXIgdGhvdWdodHMhJyxcbiAgICAgICAgICBzdWJtaXQ6ICdQdWJsaXNoIENvbW1lbnQnLFxuICAgICAgICAgIHJlcGx5OiAnUmVwbHknLFxuICAgICAgICAgIGNhbmNlbFJlcGx5OiAnQ2FuY2VsIFJlcGx5JyxcbiAgICAgICAgICBjb21tZW50OiAnQ29tbWVudCcsXG4gICAgICAgICAgcmVmcmVzaDogJ1JlZnJlc2gnLFxuICAgICAgICAgIG1vcmU6ICdMb2FkIE1vcmUgQ29tbWVudHMuLi4nLFxuICAgICAgICAgIHByZXZpZXc6ICdQcmV2aWV3JyxcbiAgICAgICAgICBlbW9qaTogJ0Vtb2ppJyxcbiAgICAgICAgICB1cGxvYWRJbWFnZTogJ1VwbG9hZCBJbWFnZScsXG4gICAgICAgICAgc2Vjb25kczogJ3NlY29uZHMgYWdvJyxcbiAgICAgICAgICBtaW51dGVzOiAnbWludXRlcyBhZ28nLFxuICAgICAgICAgIGhvdXJzOiAnaG91cnMgYWdvJyxcbiAgICAgICAgICBkYXlzOiAnZGF5cyBhZ28nLFxuICAgICAgICAgIG5vdzogJ2p1c3Qgbm93J1xuICAgICAgICB9LFxuICAgICAgICBlbW9qaTogY2RuUHJvdmlkZXIgPT09ICdqc2RlbGl2cicgPyBbXG4gICAgICAgICAgJ2h0dHBzOi8vY2RuLmpzZGVsaXZyLm5ldC9ucG0vQHdhbGluZS9lbW9qaXNAMS4yLjAvYWx1cycsXG4gICAgICAgICAgJ2h0dHBzOi8vY2RuLmpzZGVsaXZyLm5ldC9ucG0vQHdhbGluZS9lbW9qaXNAMS4yLjAvYmlsaWJpbGknLFxuICAgICAgICAgICdodHRwczovL2Nkbi5qc2RlbGl2ci5uZXQvbnBtL0B3YWxpbmUvZW1vamlzQDEuMi4wL2Jtb2ppJyxcbiAgICAgICAgICAnaHR0cHM6Ly9jZG4uanNkZWxpdnIubmV0L25wbS9Ad2FsaW5lL2Vtb2ppc0AxLjIuMC9xcScsXG4gICAgICAgICAgJ2h0dHBzOi8vY2RuLmpzZGVsaXZyLm5ldC9ucG0vQHdhbGluZS9lbW9qaXNAMS4yLjAvdGllYmEnLFxuICAgICAgICAgICdodHRwczovL2Nkbi5qc2RlbGl2ci5uZXQvbnBtL0B3YWxpbmUvZW1vamlzQDEuMi4wL3R3LWVtb2ppJyxcbiAgICAgICAgICAnaHR0cHM6Ly9jZG4uanNkZWxpdnIubmV0L25wbS9Ad2FsaW5lL2Vtb2ppc0AxLjIuMC93ZWlibycsXG4gICAgICAgICAgJ2h0dHBzOi8vY2RuLmpzZGVsaXZyLm5ldC9ucG0vQHdhbGluZS9lbW9qaXNAMS4yLjAvc291bC1lbW9qaScsXG4gICAgICAgIF0gOiBbXG4gICAgICAgICAgJ2h0dHBzOi8vdW5wa2cuY29tL0B3YWxpbmUvZW1vamlzQDEuMi4wL2FsdXMnLFxuICAgICAgICAgICdodHRwczovL3VucGtnLmNvbS9Ad2FsaW5lL2Vtb2ppc0AxLjIuMC9iaWxpYmlsaScsXG4gICAgICAgICAgJ2h0dHBzOi8vdW5wa2cuY29tL0B3YWxpbmUvZW1vamlzQDEuMi4wL2Jtb2ppJyxcbiAgICAgICAgICAnaHR0cHM6Ly91bnBrZy5jb20vQHdhbGluZS9lbW9qaXNAMS4yLjAvcXEnLFxuICAgICAgICAgICdodHRwczovL3VucGtnLmNvbS9Ad2FsaW5lL2Vtb2ppc0AxLjIuMC90aWViYScsXG4gICAgICAgICAgJ2h0dHBzOi8vdW5wa2cuY29tL0B3YWxpbmUvZW1vamlzQDEuMi4wL3R3LWVtb2ppJyxcbiAgICAgICAgICAnaHR0cHM6Ly91bnBrZy5jb20vQHdhbGluZS9lbW9qaXNAMS4yLjAvd2VpYm8nLFxuICAgICAgICAgICdodHRwczovL3VucGtnLmNvbS9Ad2FsaW5lL2Vtb2ppc0AxLjIuMC9zb3VsLWVtb2ppJyxcbiAgICAgICAgXSxcbiAgICAgICAgbWV0YTogWyduaWNrJywgJ21haWwnLCAnbGluayddLFxuICAgICAgICByZXF1aXJlZE1ldGE6IFsnbmljayddLFxuICAgICAgICBsb2dpbjogJ2VuYWJsZScsXG4gICAgICAgIHdvcmRMaW1pdDogWzAsIDEwMDBdLFxuICAgICAgICBwYWdlU2l6ZTogMTAsXG4gICAgICAgIGxhbmc6ICdlbi1VUycsXG4gICAgICAgIHJlYWN0aW9uOiB0cnVlLFxuICAgICAgICBpbWFnZVVwbG9hZGVyOiBmYWxzZSxcbiAgICAgICAgdGV4UmVuZGVyZXI6IGZhbHNlLFxuICAgICAgICBzZWFyY2g6IGZhbHNlXG4gICAgICB9KVxuXG4gICAgICBpZiAoIWFib3J0Q29udHJvbGxlclJlZi5jdXJyZW50Py5zaWduYWwuYWJvcnRlZCkge1xuICAgICAgICBzZXRXYWxpbmVJbnN0YW5jZShpbnN0YW5jZSlcbiAgICAgICAgc2V0SXNJbml0aWFsaXplZCh0cnVlKVxuICAgICAgICBzZXRJc0xvYWRpbmcoZmFsc2UpXG4gICAgICAgIFxuICAgICAgICAvLyDmt7vliqDliqDovb3lrozmiJDnmoTlm57osIPlkozkv67lpI3ooajmg4XlvLnnqpdcbiAgICAgICAgc2V0VGltZW91dCgoKSA9PiB7XG4gICAgICAgICAgaWYgKHdhbGluZVJlZi5jdXJyZW50KSB7XG4gICAgICAgICAgICB3YWxpbmVSZWYuY3VycmVudC5jbGFzc0xpc3QuYWRkKCd3YWxpbmUtbG9hZGVkJylcbiAgICAgICAgICAgIGZpeEVtb2ppUG9wdXBzKClcbiAgICAgICAgICAgIGVuaGFuY2VBdmF0YXJzKClcblxuICAgICAgICAgICAgLy8g5L+u5aSN6KGo5oOF5YyF5Zu+54mH5Yqg6L29XG4gICAgICAgICAgICBjb25zdCBmaXhFbW9qaUltYWdlcyA9ICgpID0+IHtcbiAgICAgICAgICAgICAgY29uc3QgZW1vamlJbWFnZXMgPSB3YWxpbmVSZWYuY3VycmVudD8ucXVlcnlTZWxlY3RvckFsbCgnLndsLWVtb2ppIGltZycpXG4gICAgICAgICAgICAgIGVtb2ppSW1hZ2VzPy5mb3JFYWNoKChpbWc6IEVsZW1lbnQpID0+IHtcbiAgICAgICAgICAgICAgICBjb25zdCBpbWdFbGVtZW50ID0gaW1nIGFzIEhUTUxJbWFnZUVsZW1lbnRcblxuICAgICAgICAgICAgICAgIGlmICghaW1nRWxlbWVudC5jb21wbGV0ZSkge1xuICAgICAgICAgICAgICAgICAgaW1nRWxlbWVudC5hZGRFdmVudExpc3RlbmVyKCdlcnJvcicsICgpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgY29uc29sZS53YXJuKCfooajmg4XljIXlm77niYfliqDovb3lpLHotKU6JywgaW1nRWxlbWVudC5zcmMpXG4gICAgICAgICAgICAgICAgICAgIGltZ0VsZW1lbnQuc3R5bGUuYmFja2dyb3VuZENvbG9yID0gJyNmMGYwZjAnXG4gICAgICAgICAgICAgICAgICAgIGltZ0VsZW1lbnQuc3R5bGUuYm9yZGVyID0gJzFweCBzb2xpZCAjZGRkJ1xuICAgICAgICAgICAgICAgICAgfSlcblxuICAgICAgICAgICAgICAgICAgaW1nRWxlbWVudC5hZGRFdmVudExpc3RlbmVyKCdsb2FkJywgKCkgPT4ge1xuICAgICAgICAgICAgICAgICAgICBpbWdFbGVtZW50LnN0eWxlLmJhY2tncm91bmRDb2xvciA9ICd0cmFuc3BhcmVudCdcbiAgICAgICAgICAgICAgICAgICAgaW1nRWxlbWVudC5zdHlsZS5ib3JkZXIgPSAnbm9uZSdcbiAgICAgICAgICAgICAgICAgIH0pXG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICB9KVxuICAgICAgICAgICAgfVxuXG4gICAgICAgICAgICBmaXhFbW9qaUltYWdlcygpXG5cbiAgICAgICAgICAgIC8vIOmineWklueahOihqOaDheWMheW8ueeql+S/ruWkjSAtIOehruS/neaJgOacieeOsOacieeahOihqOaDheWMheW8ueeql+mDveiiq+makOiXj1xuICAgICAgICAgICAgY29uc3QgZXhpc3RpbmdFbW9qaVBvcHVwcyA9IHdhbGluZVJlZi5jdXJyZW50LnF1ZXJ5U2VsZWN0b3JBbGwoJy53bC1lbW9qaScpXG4gICAgICAgICAgICBleGlzdGluZ0Vtb2ppUG9wdXBzLmZvckVhY2goKHBvcHVwKSA9PiB7XG4gICAgICAgICAgICAgIGlmICghcG9wdXAuY2xhc3NMaXN0LmNvbnRhaW5zKCd3bC1lbW9qaS1vcGVuJykpIHtcbiAgICAgICAgICAgICAgICBwb3B1cC5zZXRBdHRyaWJ1dGUoJ3N0eWxlJywgJ2Rpc3BsYXk6IG5vbmUgIWltcG9ydGFudDsgb3BhY2l0eTogMCAhaW1wb3J0YW50OyB2aXNpYmlsaXR5OiBoaWRkZW4gIWltcG9ydGFudDsnKVxuICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9KVxuICAgICAgICAgIH1cbiAgICAgICAgfSwgMzAwKVxuXG4gICAgICAgIC8vIOebkeWQrERPTeWPmOWMluS7peW6lOeUqOWktOWDj+WinuW8uuWSjOihqOaDheWMheS/ruWkjVxuICAgICAgICBjb25zdCBvYnNlcnZlciA9IG5ldyBNdXRhdGlvbk9ic2VydmVyKChtdXRhdGlvbnMpID0+IHtcbiAgICAgICAgICBlbmhhbmNlQXZhdGFycygpXG5cbiAgICAgICAgICAvLyDmo4Dmn6XmmK/lkKbmnInmlrDnmoTooajmg4XljIXlvLnnqpfooqvliJvlu7pcbiAgICAgICAgICBtdXRhdGlvbnMuZm9yRWFjaCgobXV0YXRpb24pID0+IHtcbiAgICAgICAgICAgIG11dGF0aW9uLmFkZGVkTm9kZXMuZm9yRWFjaCgobm9kZSkgPT4ge1xuICAgICAgICAgICAgICBpZiAobm9kZS5ub2RlVHlwZSA9PT0gTm9kZS5FTEVNRU5UX05PREUpIHtcbiAgICAgICAgICAgICAgICBjb25zdCBlbGVtZW50ID0gbm9kZSBhcyBFbGVtZW50XG4gICAgICAgICAgICAgICAgLy8g5qOA5p+l5piv5ZCm5piv6KGo5oOF5YyF5by556qX5oiW5YyF5ZCr6KGo5oOF5YyF5by556qXXG4gICAgICAgICAgICAgICAgY29uc3QgZW1vamlQb3B1cCA9IGVsZW1lbnQuY2xhc3NMaXN0Py5jb250YWlucygnd2wtZW1vamknKSA/IGVsZW1lbnQgOlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGVsZW1lbnQucXVlcnlTZWxlY3Rvcj8uKCcud2wtZW1vamknKVxuXG4gICAgICAgICAgICAgICAgaWYgKGVtb2ppUG9wdXApIHtcbiAgICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKCfmo4DmtYvliLDmlrDnmoTooajmg4XljIXlvLnnqpfvvIzlupTnlKjkv67lpI0nKVxuICAgICAgICAgICAgICAgICAgLy8g56Gu5L+d5paw5Yib5bu655qE6KGo5oOF5YyF5by556qX5Yid5aeL54q25oCB5piv6ZqQ6JeP55qEXG4gICAgICAgICAgICAgICAgICBlbW9qaVBvcHVwLmNsYXNzTGlzdC5yZW1vdmUoJ3dsLWVtb2ppLW9wZW4nKVxuICAgICAgICAgICAgICAgICAgZW1vamlQb3B1cC5zZXRBdHRyaWJ1dGUoJ3N0eWxlJywgJ2Rpc3BsYXk6IG5vbmUgIWltcG9ydGFudDsgb3BhY2l0eTogMCAhaW1wb3J0YW50OyB2aXNpYmlsaXR5OiBoaWRkZW4gIWltcG9ydGFudDsnKVxuXG4gICAgICAgICAgICAgICAgICAvLyDkv67lpI3mlrDliJvlu7rnmoTooajmg4XljIXlm77niYdcbiAgICAgICAgICAgICAgICAgIHNldFRpbWVvdXQoKCkgPT4ge1xuICAgICAgICAgICAgICAgICAgICBjb25zdCBlbW9qaUltYWdlcyA9IGVtb2ppUG9wdXAucXVlcnlTZWxlY3RvckFsbCgnaW1nJylcbiAgICAgICAgICAgICAgICAgICAgZW1vamlJbWFnZXMuZm9yRWFjaCgoaW1nOiBFbGVtZW50LCBpbmRleDogbnVtYmVyKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgY29uc3QgaW1nRWxlbWVudCA9IGltZyBhcyBIVE1MSW1hZ2VFbGVtZW50XG5cbiAgICAgICAgICAgICAgICAgICAgICBjb25zdCBoYW5kbGVMb2FkID0gKCkgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgY29uc29sZS5sb2coYOaWsOihqOaDheWMhSAke2luZGV4fSDliqDovb3miJDlip86YCwgaW1nRWxlbWVudC5zcmMpXG4gICAgICAgICAgICAgICAgICAgICAgICBpbWdFbGVtZW50LmNsYXNzTGlzdC5yZW1vdmUoJ2xvYWRpbmcnLCAnZXJyb3InKVxuICAgICAgICAgICAgICAgICAgICAgIH1cblxuICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IGhhbmRsZUVycm9yID0gKCkgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgY29uc29sZS53YXJuKGDmlrDooajmg4XljIUgJHtpbmRleH0g5Yqg6L295aSx6LSlOmAsIGltZ0VsZW1lbnQuc3JjKVxuICAgICAgICAgICAgICAgICAgICAgICAgaW1nRWxlbWVudC5jbGFzc0xpc3QuYWRkKCdlcnJvcicpXG5cbiAgICAgICAgICAgICAgICAgICAgICAgIC8vIOWwneivleWkh+eUqENETlxuICAgICAgICAgICAgICAgICAgICAgICAgY29uc3Qgb3JpZ2luYWxTcmMgPSBpbWdFbGVtZW50LnNyY1xuICAgICAgICAgICAgICAgICAgICAgICAgaWYgKG9yaWdpbmFsU3JjLmluY2x1ZGVzKCdjZG4uanNkZWxpdnIubmV0JykpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgYmFja3VwU3JjID0gb3JpZ2luYWxTcmMucmVwbGFjZSgnY2RuLmpzZGVsaXZyLm5ldC9ucG0nLCAndW5wa2cuY29tJylcbiAgICAgICAgICAgICAgICAgICAgICAgICAgaW1nRWxlbWVudC5zcmMgPSBiYWNrdXBTcmNcbiAgICAgICAgICAgICAgICAgICAgICAgIH0gZWxzZSBpZiAob3JpZ2luYWxTcmMuaW5jbHVkZXMoJ3VucGtnLmNvbScpKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IGJhY2t1cFNyYyA9IG9yaWdpbmFsU3JjLnJlcGxhY2UoJ3VucGtnLmNvbScsICdjZG4uanNkZWxpdnIubmV0L25wbScpXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGltZ0VsZW1lbnQuc3JjID0gYmFja3VwU3JjXG4gICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgfVxuXG4gICAgICAgICAgICAgICAgICAgICAgaW1nRWxlbWVudC5hZGRFdmVudExpc3RlbmVyKCdsb2FkJywgaGFuZGxlTG9hZCwgeyBvbmNlOiB0cnVlIH0pXG4gICAgICAgICAgICAgICAgICAgICAgaW1nRWxlbWVudC5hZGRFdmVudExpc3RlbmVyKCdlcnJvcicsIGhhbmRsZUVycm9yLCB7IG9uY2U6IHRydWUgfSlcbiAgICAgICAgICAgICAgICAgICAgfSlcbiAgICAgICAgICAgICAgICAgIH0sIDEwMClcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH0pXG4gICAgICAgICAgfSlcbiAgICAgICAgfSlcblxuICAgICAgICBvYnNlcnZlci5vYnNlcnZlKHdhbGluZVJlZi5jdXJyZW50LCB7XG4gICAgICAgICAgY2hpbGRMaXN0OiB0cnVlLFxuICAgICAgICAgIHN1YnRyZWU6IHRydWVcbiAgICAgICAgfSlcblxuICAgICAgICAvLyDmuIXnkIblh73mlbDkuK3mlq3lvIDop4Llr5/lmahcbiAgICAgICAgY29uc3Qgb3JpZ2luYWxDbGVhbnVwID0gKCkgPT4ge1xuICAgICAgICAgIG9ic2VydmVyLmRpc2Nvbm5lY3QoKVxuICAgICAgICB9XG5cbiAgICAgICAgLy8g5bCG5riF55CG5Ye95pWw5re75Yqg5Yiw57uE5Lu25Y246L295pe25omn6KGMXG4gICAgICAgIHJldHVybiBvcmlnaW5hbENsZWFudXBcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgaWYgKGVycm9yIGluc3RhbmNlb2YgRXJyb3IgJiYgZXJyb3IubmFtZSAhPT0gJ0Fib3J0RXJyb3InKSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ1dhbGluZSBpbml0aWFsaXphdGlvbiBlcnJvcjonLCBlcnJvcilcbiAgICAgICAgc2V0RXJyb3IoJ0ZhaWxlZCB0byBsb2FkIGNvbW1lbnRzLiBQbGVhc2UgcmVmcmVzaCB0aGUgcGFnZS4nKVxuICAgICAgICBzZXRJc0xvYWRpbmcoZmFsc2UpXG4gICAgICB9XG4gICAgfVxuICB9LCBbcGF0aCwgcmVzb2x2ZWRUaGVtZSwgc2FmZURlc3Ryb3ldKVxuXG4gIC8vIOS4u3VzZUVmZmVjdCAtIOWkhOeQhuWIneWni+WMluWSjOa4heeQhlxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIHNldElzSW5pdGlhbGl6ZWQoZmFsc2UpXG4gICAgXG4gICAgLy8g5L2/55Soc2V0VGltZW91dOmBv+WFjeWcqFJlYWN05Lil5qC85qih5byP5LiL55qE5Y+M6YeN5Yid5aeL5YyWXG4gICAgaW5pdFRpbWVvdXRSZWYuY3VycmVudCA9IHNldFRpbWVvdXQoKCkgPT4ge1xuICAgICAgaW5pdFdhbGluZSgpXG4gICAgfSwgNTApXG5cbiAgICByZXR1cm4gKCkgPT4ge1xuICAgICAgLy8g5riF55CGdGltZW91dFxuICAgICAgaWYgKGluaXRUaW1lb3V0UmVmLmN1cnJlbnQpIHtcbiAgICAgICAgY2xlYXJUaW1lb3V0KGluaXRUaW1lb3V0UmVmLmN1cnJlbnQpXG4gICAgICB9XG5cbiAgICAgIC8vIOWPlua2iOivt+axglxuICAgICAgaWYgKGFib3J0Q29udHJvbGxlclJlZi5jdXJyZW50KSB7XG4gICAgICAgIGFib3J0Q29udHJvbGxlclJlZi5jdXJyZW50LmFib3J0KClcbiAgICAgIH1cblxuICAgICAgLy8g5a6J5YWo6ZSA5q+B5a6e5L6LXG4gICAgICBpZiAod2FsaW5lSW5zdGFuY2UpIHtcbiAgICAgICAgc2FmZURlc3Ryb3kod2FsaW5lSW5zdGFuY2UpXG4gICAgICB9XG5cbiAgICAgIHNldElzSW5pdGlhbGl6ZWQoZmFsc2UpXG4gICAgICBzZXRXYWxpbmVJbnN0YW5jZShudWxsKVxuICAgICAgc2V0SXNMb2FkaW5nKHRydWUpXG4gICAgICBzZXRFcnJvcihudWxsKVxuICAgIH1cbiAgfSwgW3BhdGgsIHJlc29sdmVkVGhlbWUsIGluaXRXYWxpbmUsIHNhZmVEZXN0cm95XSlcblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPXtgd2FsaW5lLWNvbnRhaW5lci1wcmVtaXVtICR7Y2xhc3NOYW1lfWB9PlxuICAgICAgey8qIOmUmeivr+eKtuaAgSAqL31cbiAgICAgIHtlcnJvciAmJiAoXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItNiBwLTQgYmctZGVzdHJ1Y3RpdmUvMTAgYm9yZGVyIGJvcmRlci1kZXN0cnVjdGl2ZS8yMCByb3VuZGVkLXhsIHRleHQtZGVzdHJ1Y3RpdmUgdGV4dC1zbSB0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgIDxwPntlcnJvcn08L3A+XG4gICAgICAgICAgPGJ1dHRvbiBcbiAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHtcbiAgICAgICAgICAgICAgc2V0RXJyb3IobnVsbClcbiAgICAgICAgICAgICAgaW5pdFdhbGluZSgpXG4gICAgICAgICAgICB9fVxuICAgICAgICAgICAgY2xhc3NOYW1lPVwibXQtMiBweC0zIHB5LTEgYmctZGVzdHJ1Y3RpdmUvMjAgaG92ZXI6YmctZGVzdHJ1Y3RpdmUvMzAgcm91bmRlZC1tZCB0cmFuc2l0aW9uLWNvbG9yc1wiXG4gICAgICAgICAgPlxuICAgICAgICAgICAgUmV0cnlcbiAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgPC9kaXY+XG4gICAgICApfVxuICAgICAgXG4gICAgICB7Lyog5Li76KaB55qEV2FsaW5l5a655ZmoICovfVxuICAgICAgPGRpdiBcbiAgICAgICAgcmVmPXt3YWxpbmVSZWZ9IFxuICAgICAgICBjbGFzc05hbWU9e2B3YWxpbmUtd3JhcHBlci1wcmVtaXVtIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTUwMCAke1xuICAgICAgICAgIGlzSW5pdGlhbGl6ZWQgPyAnb3BhY2l0eS0xMDAnIDogJ29wYWNpdHktMCdcbiAgICAgICAgfWB9XG4gICAgICAgIHN0eWxlPXt7XG4gICAgICAgICAgLy8g6Ieq5a6a5LmJQ1NT5Y+Y6YeP5Lul6YCC6YWN6K6+6K6h57O757ufXG4gICAgICAgICAgJy0td2FsaW5lLXRoZW1lLWNvbG9yJzogJ2hzbCh2YXIoLS1wcmltYXJ5KSknLFxuICAgICAgICAgICctLXdhbGluZS1hY3RpdmUtY29sb3InOiAnaHNsKHZhcigtLXByaW1hcnkpKScsXG4gICAgICAgICAgJy0td2FsaW5lLWJvcmRlci1jb2xvcic6ICdoc2wodmFyKC0tYm9yZGVyKSknLFxuICAgICAgICAgICctLXdhbGluZS1iZy1jb2xvcic6ICdoc2wodmFyKC0tYmFja2dyb3VuZCkpJyxcbiAgICAgICAgICAnLS13YWxpbmUtYmctY29sb3ItbGlnaHQnOiAnaHNsKHZhcigtLW11dGVkKSknLFxuICAgICAgICAgICctLXdhbGluZS10ZXh0LWNvbG9yJzogJ2hzbCh2YXIoLS1mb3JlZ3JvdW5kKSknLFxuICAgICAgICAgICctLXdhbGluZS1saWdodC1ncmV5JzogJ2hzbCh2YXIoLS1tdXRlZC1mb3JlZ3JvdW5kKSknLFxuICAgICAgICAgICctLXdhbGluZS13aGl0ZSc6ICdoc2wodmFyKC0tY2FyZCkpJyxcbiAgICAgICAgICAnLS13YWxpbmUtY29sb3InOiAnaHNsKHZhcigtLWZvcmVncm91bmQpKScsXG4gICAgICAgICAgJy0td2FsaW5lLWJvcmRlci1yYWRpdXMnOiAnMC43NXJlbScsXG4gICAgICAgICAgJy0td2FsaW5lLWF2YXRhci1zaXplJzogJzIuNXJlbScsXG4gICAgICAgICAgbWluSGVpZ2h0OiBpc0luaXRpYWxpemVkID8gJ2F1dG8nIDogJzMwMHB4J1xuICAgICAgICB9IGFzIFJlYWN0LkNTU1Byb3BlcnRpZXN9XG4gICAgICAvPlxuICAgICAgXG4gICAgICB7Lyog5Yqg6L2954q25oCBICovfVxuICAgICAge2lzTG9hZGluZyAmJiAhZXJyb3IgJiYgKFxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC1jb2wgaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHB5LTE2IHNwYWNlLXktNFwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmVcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0xMiBoLTEyIGJvcmRlci00IGJvcmRlci1tdXRlZC1mb3JlZ3JvdW5kLzIwIGJvcmRlci10LXByaW1hcnkgcm91bmRlZC1mdWxsIGFuaW1hdGUtc3BpblwiIC8+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIGluc2V0LTAgdy0xMiBoLTEyIGJvcmRlci00IGJvcmRlci10cmFuc3BhcmVudCBib3JkZXItci1wcmltYXJ5LzUwIHJvdW5kZWQtZnVsbCBhbmltYXRlLXNwaW5cIiBzdHlsZT17eyBhbmltYXRpb25EaXJlY3Rpb246ICdyZXZlcnNlJywgYW5pbWF0aW9uRHVyYXRpb246ICcwLjhzJyB9fSAvPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgc3BhY2UteS0xXCI+XG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZm9yZWdyb3VuZFwiPkxvYWRpbmcgRGlzY3Vzc2lvbjwvcD5cbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCI+UHJlcGFyaW5nIGNvbW1lbnQgc3lzdGVtLi4uPC9wPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgICl9XG4gICAgPC9kaXY+XG4gIClcbn1cblxuZXhwb3J0IGRlZmF1bHQgV2FsaW5lQ29tbWVudCJdLCJuYW1lcyI6WyJpbml0IiwidXNlRWZmZWN0IiwidXNlUmVmIiwidXNlU3RhdGUiLCJ1c2VDYWxsYmFjayIsInVzZVRoZW1lIiwiZ2V0V2FsaW5lQ29uZmlnIiwiV2FsaW5lQ29tbWVudCIsInBhdGgiLCJ0aXRsZSIsImNsYXNzTmFtZSIsIndhbGluZVJlZiIsInJlc29sdmVkVGhlbWUiLCJ3YWxpbmVJbnN0YW5jZSIsInNldFdhbGluZUluc3RhbmNlIiwiaXNJbml0aWFsaXplZCIsInNldElzSW5pdGlhbGl6ZWQiLCJpc0xvYWRpbmciLCJzZXRJc0xvYWRpbmciLCJlcnJvciIsInNldEVycm9yIiwiaW5pdFRpbWVvdXRSZWYiLCJhYm9ydENvbnRyb2xsZXJSZWYiLCJzYWZlRGVzdHJveSIsImluc3RhbmNlIiwiZGVzdHJveSIsImNvbnNvbGUiLCJ3YXJuIiwiZ2VuZXJhdGVSYW5kb21BdmF0YXIiLCJzZWVkIiwiYXZhdGFyU3R5bGVzIiwic3R5bGVJbmRleCIsInNwbGl0IiwicmVkdWNlIiwiYWNjIiwiY2hhciIsImNoYXJDb2RlQXQiLCJsZW5ndGgiLCJzZWxlY3RlZFN0eWxlIiwiZW5jb2RlVVJJQ29tcG9uZW50IiwiZW5oYW5jZUF2YXRhcnMiLCJjdXJyZW50IiwiY29udGFpbmVyIiwiYXZhdGFycyIsInF1ZXJ5U2VsZWN0b3JBbGwiLCJmb3JFYWNoIiwiYXZhdGFyIiwiY2xhc3NMaXN0IiwiY29udGFpbnMiLCJhZGQiLCJpbWciLCJxdWVyeVNlbGVjdG9yIiwiY29tbWVudENhcmQiLCJjbG9zZXN0IiwidXNlck5pY2siLCJ0ZXh0Q29udGVudCIsInVzZXJNYWlsIiwiTWF0aCIsInJhbmRvbSIsInRvU3RyaW5nIiwic3Vic3RyIiwiYXZhdGFyVXJsIiwiYXZhdGFySW1nIiwiZG9jdW1lbnQiLCJjcmVhdGVFbGVtZW50Iiwic3JjIiwiYWx0Iiwic3R5bGUiLCJjc3NUZXh0Iiwib25lcnJvciIsInNldEF0dHJpYnV0ZSIsInJlbW92ZSIsImFwcGVuZENoaWxkIiwicmVtb3ZlQXR0cmlidXRlIiwiZml4RW1vamlQb3B1cHMiLCJmaXhFbW9qaUltYWdlcyIsImVtb2ppSW1hZ2VzIiwibG9nIiwiaW5kZXgiLCJpbWdFbGVtZW50IiwiY29tcGxldGUiLCJuYXR1cmFsV2lkdGgiLCJoYW5kbGVMb2FkIiwiYmFja2dyb3VuZENvbG9yIiwiYm9yZGVyIiwiaGFuZGxlRXJyb3IiLCJvcmlnaW5hbFNyYyIsImluY2x1ZGVzIiwiYmFja3VwU3JjIiwicmVwbGFjZSIsInJlbW92ZUV2ZW50TGlzdGVuZXIiLCJhZGRFdmVudExpc3RlbmVyIiwib25jZSIsIndpbmRvdyIsImxvY2F0aW9uIiwiaHJlZiIsImVtb2ppQnV0dG9ucyIsImJ1dHRvbiIsImdldEF0dHJpYnV0ZSIsImUiLCJzZXRUaW1lb3V0IiwiZW1vamlQb3B1cCIsImRpc3BsYXkiLCJvcGFjaXR5IiwidmlzaWJpbGl0eSIsImhlYWRlciIsImlubmVySFRNTCIsImluc2VydEJlZm9yZSIsImZpcnN0Q2hpbGQiLCJjbG9zZUJ0biIsImNsb3NlRW1vamlQb3B1cCIsImhhbmRsZUJhY2tncm91bmRDbGljayIsInRhcmdldCIsImhhbmRsZUVzY0tleSIsImtleSIsInBvcHVwIiwiZ2lmQnV0dG9ucyIsImdpZlBvcHVwIiwidGVzdEVtb2ppQ0ROIiwidGVzdFVybHMiLCJ1cmwiLCJjb250cm9sbGVyIiwiQWJvcnRDb250cm9sbGVyIiwidGltZW91dElkIiwiYWJvcnQiLCJyZXNwb25zZSIsImZldGNoIiwibWV0aG9kIiwic2lnbmFsIiwiY2xlYXJUaW1lb3V0Iiwib2siLCJpbml0V2FsaW5lIiwiUHJvbWlzZSIsInJlc29sdmUiLCJhYm9ydGVkIiwiY2RuUHJvdmlkZXIiLCJjb25maWciLCJlbCIsInNlcnZlclVSTCIsImRhcmsiLCJsb2NhbGUiLCJwbGFjZWhvbGRlciIsImFkbWluIiwibGV2ZWwwIiwibGV2ZWwxIiwibGV2ZWwyIiwibGV2ZWwzIiwibGV2ZWw0IiwibGV2ZWw1IiwiYW5vbnltb3VzIiwibG9naW4iLCJsb2dvdXQiLCJwcm9maWxlIiwibmlja0Vycm9yIiwibWFpbEVycm9yIiwid29yZEhpbnQiLCJzb2ZhIiwic3VibWl0IiwicmVwbHkiLCJjYW5jZWxSZXBseSIsImNvbW1lbnQiLCJyZWZyZXNoIiwibW9yZSIsInByZXZpZXciLCJlbW9qaSIsInVwbG9hZEltYWdlIiwic2Vjb25kcyIsIm1pbnV0ZXMiLCJob3VycyIsImRheXMiLCJub3ciLCJtZXRhIiwicmVxdWlyZWRNZXRhIiwid29yZExpbWl0IiwicGFnZVNpemUiLCJsYW5nIiwicmVhY3Rpb24iLCJpbWFnZVVwbG9hZGVyIiwidGV4UmVuZGVyZXIiLCJzZWFyY2giLCJleGlzdGluZ0Vtb2ppUG9wdXBzIiwib2JzZXJ2ZXIiLCJNdXRhdGlvbk9ic2VydmVyIiwibXV0YXRpb25zIiwibXV0YXRpb24iLCJhZGRlZE5vZGVzIiwibm9kZSIsIm5vZGVUeXBlIiwiTm9kZSIsIkVMRU1FTlRfTk9ERSIsImVsZW1lbnQiLCJvYnNlcnZlIiwiY2hpbGRMaXN0Iiwic3VidHJlZSIsIm9yaWdpbmFsQ2xlYW51cCIsImRpc2Nvbm5lY3QiLCJFcnJvciIsIm5hbWUiLCJkaXYiLCJwIiwib25DbGljayIsInJlZiIsIm1pbkhlaWdodCIsImFuaW1hdGlvbkRpcmVjdGlvbiIsImFuaW1hdGlvbkR1cmF0aW9uIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/comment/WalineComment.tsx\n"));

/***/ })

});